import React, { useState } from 'react';
import {
  Container,
  Typography,
  Tabs,
  Tab,
  Box,
  Paper,
} from '@mui/material';
import { useBaselinkStore } from '../../store/baselinkStore';
import ConnectionStatus from './ConnectionStatus';
import VehicleSelector from './VehicleSelector';
import TripIdInput from './TripIdInput';
import TripPreview from './TripPreview';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`trip-loading-tabpanel-${index}`}
      aria-labelledby={`trip-loading-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const TripLoadingPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [selectedVehicleId, setSelectedVehicleId] = useState<string | undefined>();
  
  const { 
    currentTrip, 
    tripLoading, 
    fetchTripById, 
    loadTripIntoSimulation,
    selectVehicle,
    vehicles 
  } = useBaselinkStore();
  
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleVehicleSelect = (vehicleId: string) => {
    setSelectedVehicleId(vehicleId);
    const vehicle = vehicles.find(v => v.id === vehicleId);
    if (vehicle) {
      selectVehicle(vehicle);
    }
  };

  const handleTripFetch = async (tripId: string) => {
    await fetchTripById(tripId);
  };

  const handleLoadTrip = async () => {
    try {
      const success = await loadTripIntoSimulation();
      if (success) {
        // Optionally navigate to simulation page
        console.log('Trip loaded successfully');
      }
    } catch (error) {
      console.error('Failed to load trip:', error);
    }
  };

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Trip Loading
      </Typography>
      
      <ConnectionStatus />
      
      <Paper sx={{ mt: 2 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Vehicle Selection" />
            <Tab label="Trip ID Input" />
          </Tabs>
        </Box>
        
        <TabPanel value={tabValue} index={0}>
          <VehicleSelector
            onVehicleSelect={handleVehicleSelect}
            selectedVehicleId={selectedVehicleId}
          />
          
          <TripPreview
            trip={currentTrip}
            isLoading={tripLoading === 'loading'}
            onLoadTrip={handleLoadTrip}
            isLoadingIntoSimulation={false}
          />
        </TabPanel>
        
        <TabPanel value={tabValue} index={1}>
          <TripIdInput
            onTripFetch={handleTripFetch}
            isLoading={tripLoading === 'loading'}
            error={tripLoading === 'error' ? 'Failed to fetch trip' : null}
          />
          
          <Box sx={{ mt: 3 }}>
            <TripPreview
              trip={currentTrip}
              isLoading={tripLoading === 'loading'}
              onLoadTrip={handleLoadTrip}
              isLoadingIntoSimulation={false}
            />
          </Box>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default TripLoadingPage; 