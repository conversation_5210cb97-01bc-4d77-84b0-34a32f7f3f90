import React, { useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Chip,
} from '@mui/material';
import { CheckCircle, Error } from '@mui/icons-material';
import { useBaselinkStore } from '../../store/baselinkStore';

const ConnectionStatus: React.FC = () => {
  const { 
    connectionStatus, 
    connectionLoading,
    fetchConnectionStatus 
  } = useBaselinkStore();

  // Fetch connection status on mount and set up periodic refresh
  useEffect(() => {
    // Initial fetch
    fetchConnectionStatus();

    // Set up periodic refresh every 5 seconds
    const interval = setInterval(() => {
      fetchConnectionStatus();
    }, 5000);

    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, [fetchConnectionStatus]);

  const getStatusIcon = () => {
    if (connectionStatus?.connected) {
      return <CheckCircle color="success" />;
    } else {
      return <Error color="error" />;
    }
  };

  const getStatusColor = () => {
    if (connectionStatus?.connected) {
      return 'success';
    } else {
      return 'error';
    }
  };

  return (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Box display="flex" alignItems="center" gap={2}>
        {getStatusIcon()}
        <Box>
          <Typography variant="h6" component="div">
            Connection Status
          </Typography>
          <Box display="flex" alignItems="center" gap={1} mt={0.5}>
            <Chip
              label={connectionStatus?.connected ? 'Connected to Baselink' : 'Disconnected'}
              color={getStatusColor()}
              size="small"
            />
            {connectionStatus?.version && (
              <Typography variant="body2" color="text.secondary">
                Version {connectionStatus.version}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
      
      {connectionLoading === 'error' && (
        <Typography variant="body2" color="error" sx={{ mt: 1 }}>
          Error: Failed to connect to Baselink
        </Typography>
      )}
    </Paper>
  );
};

export default ConnectionStatus; 