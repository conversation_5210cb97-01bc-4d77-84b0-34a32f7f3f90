import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';

interface TripIdInputProps {
  onTripFetch: (tripId: string) => void;
  isLoading?: boolean;
  error?: string | null;
}

const TripIdInput: React.FC<TripIdInputProps> = ({ 
  onTripFetch, 
  isLoading = false, 
  error 
}) => {
  const [tripId, setTripId] = useState('');
  const [inputError, setInputError] = useState<string | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate input
    if (!tripId.trim()) {
      setInputError('Trip ID is required');
      return;
    }
    
    // Clear any previous input error
    setInputError(null);
    
    // Call the fetch function
    onTripFetch(tripId.trim());
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTripId(e.target.value);
    // Clear input error when user starts typing
    if (inputError) {
      setInputError(null);
    }
  };

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Trip ID Input
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Enter a trip ID directly to fetch and load trip data.
      </Typography>

      <Box component="form" onSubmit={handleSubmit} sx={{ mb: 2 }}>
        <Box display="flex" gap={2} alignItems="flex-start">
          <TextField
            label="Trip ID"
            variant="outlined"
            value={tripId}
            onChange={handleInputChange}
            error={!!inputError}
            helperText={inputError}
            placeholder="e.g., 422250752"
            sx={{ flex: 1 }}
            disabled={isLoading}
          />
          <Button
            type="submit"
            variant="contained"
            startIcon={isLoading ? <CircularProgress size={20} /> : <SearchIcon />}
            disabled={isLoading || !tripId.trim()}
            sx={{ minWidth: 120 }}
          >
            {isLoading ? 'Loading...' : 'Get Trip'}
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error">
          {error}
        </Alert>
      )}
    </Paper>
  );
};

export default TripIdInput; 