import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Divider,
  CircularProgress,
  Card,
  CardContent,
} from '@mui/material';
import { 
  PlayArrow as PlayIcon, 
  DirectionsBus as BusIcon,
  Schedule as ScheduleIcon,
  Place as PlaceIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  FlightTakeoff as DepartureIcon
} from '@mui/icons-material';
import { Trip } from '../../services/types';
import { 
  formatDate, 
  formatTimestamp, 
  formatTimeWithDelay,
  getOperationDay, 
  getTripStartTime, 
  getTripEndTime,
  getPreferredDepartureTime
} from '../../utils/formatters';

interface TripPreviewProps {
  trip: Trip | null;
  isLoading?: boolean;
  onLoadTrip: (trip: Trip) => void;
  isLoadingIntoSimulation?: boolean;
}

const TripPreview: React.FC<TripPreviewProps> = ({ 
  trip, 
  isLoading = false, 
  onLoadTrip, 
  isLoadingIntoSimulation = false 
}) => {
  const [tripLoadedTime, setTripLoadedTime] = useState<Date | null>(null);

  useEffect(() => {
    // Update timestamp when trip data is loaded
    if (trip && !isLoading) {
      setTripLoadedTime(new Date());
    }
  }, [trip, isLoading]);

  if (isLoading) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="body2" sx={{ mt: 2 }}>
          Loading trip preview...
        </Typography>
      </Paper>
    );
  }

  if (!trip) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          Select a vehicle or enter a trip ID to preview trip details
        </Typography>
      </Paper>
    );
  }

  const handleLoadTrip = () => {
    onLoadTrip(trip);
  };

  // Calculate trip information
  const operationDay = getOperationDay(trip);
  const tripStartTime = getTripStartTime(trip);
  const tripEndTime = getTripEndTime(trip);

  return (
    <Paper sx={{ p: 3 }}>
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={3}>
        <Box>
          <Typography variant="h6" component="div">
            Trip Preview
          </Typography>
          {tripLoadedTime && (
            <Typography variant="caption" color="text.secondary">
              Loaded at {tripLoadedTime.toLocaleTimeString()}
            </Typography>
          )}
        </Box>
        <Button
          variant="contained"
          size="medium"
          startIcon={isLoadingIntoSimulation ? <CircularProgress size={16} /> : <PlayIcon />}
          onClick={handleLoadTrip}
          disabled={isLoadingIntoSimulation}
          sx={{ minWidth: 200 }}
        >
          {isLoadingIntoSimulation ? 'Loading...' : 'Load Trip into Simulation'}
        </Button>
      </Box>

      {/* Basic Trip Information */}
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3, mb: 3 }}>
        <Box sx={{ flex: '1 1 250px' }}>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <BusIcon color="primary" fontSize="small" />
            <Typography variant="body2" fontWeight="medium">
              Trip ID
            </Typography>
          </Box>
          <Typography variant="body1">
            {trip.tripId}
          </Typography>
        </Box>

        <Box sx={{ flex: '1 1 250px' }}>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <PlaceIcon color="primary" fontSize="small" />
            <Typography variant="body2" fontWeight="medium">
              Route
            </Typography>
          </Box>
          <Typography variant="body1">
            {trip.lineName ? `${trip.lineName} → ${trip.destinationText}` : trip.destinationText}
          </Typography>
        </Box>

        <Box sx={{ flex: '1 1 250px' }}>
          <Box display="flex" alignItems="center" gap={1} mb={1}>
            <ScheduleIcon color="primary" fontSize="small" />
            <Typography variant="body2" fontWeight="medium">
              Stops
            </Typography>
          </Box>
          <Typography variant="body1">
            {trip.calls?.length || 0} total
          </Typography>
        </Box>
      </Box>

      {/* Trip Timing Information */}
      <Card sx={{ mb: 3, bgcolor: 'background.default' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TimeIcon color="primary" />
            Trip Timing
          </Typography>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
            <Box sx={{ flex: '1 1 250px' }}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <CalendarIcon fontSize="small" color="action" />
                <Typography variant="body2" fontWeight="medium">
                  Operation Day
                </Typography>
              </Box>
              <Typography variant="body1">
                {operationDay ? formatDate(operationDay) : 'n/a'}
              </Typography>
            </Box>

            <Box sx={{ flex: '1 1 250px' }}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <DepartureIcon fontSize="small" color="action" />
                <Typography variant="body2" fontWeight="medium">
                  Trip Start
                </Typography>
              </Box>
              <Typography variant="body1">
                {tripStartTime ? formatTimestamp(tripStartTime) : 'n/a'}
              </Typography>
            </Box>

            <Box sx={{ flex: '1 1 250px' }}>
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                <PlaceIcon fontSize="small" color="action" />
                <Typography variant="body2" fontWeight="medium">
                  Trip End
                </Typography>
              </Box>
              <Typography variant="body1">
                {tripEndTime ? formatTimestamp(tripEndTime) : 'n/a'}
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Route Overview with Departure Times */}
      {trip.calls && trip.calls.length > 0 && (
        <>
          <Divider sx={{ mb: 2 }} />
          <Typography variant="body2" fontWeight="medium" mb={2}>
            Route Overview with Departure Times
          </Typography>
          <Box>
            {trip.calls.map((stop, index) => {
              const departureTime = getPreferredDepartureTime(stop);
              return (
                <Box 
                  key={index} 
                  sx={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    py: 0.5,
                    px: 1,
                    bgcolor: index % 2 === 0 ? 'action.hover' : 'transparent',
                    borderRadius: 1,
                    mb: 0.5
                  }}
                >
                  <Typography variant="body2" sx={{ flex: 1 }}>
                    {index + 1}. {stop.stopName}
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    sx={{ 
                      fontFamily: 'monospace',
                      minWidth: 120,
                      textAlign: 'right'
                    }}
                  >
                    {departureTime ? formatTimeWithDelay(stop.actualDepartureTime, stop.scheduledDepartureTime) : 'n/a'}
                  </Typography>
                </Box>
              );
            })}
          </Box>
        </>
      )}
    </Paper>
  );
};

export default TripPreview; 