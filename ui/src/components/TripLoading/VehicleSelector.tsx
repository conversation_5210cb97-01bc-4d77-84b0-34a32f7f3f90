import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';
import { useBaselinkStore } from '../../store/baselinkStore';

interface VehicleSelectorProps {
  onVehicleSelect: (vehicleId: string) => void;
  selectedVehicleId?: string;
}

const VehicleSelector: React.FC<VehicleSelectorProps> = ({ 
  onVehicleSelect, 
  selectedVehicleId 
}) => {
  const [lastLoadedTime, setLastLoadedTime] = useState<Date | null>(null);
  
  const { 
    vehicles, 
    vehiclesLoading, 
    fetchVehicles 
  } = useBaselinkStore();

  useEffect(() => {
    // Auto-fetch vehicles when component mounts
    fetchVehicles();
  }, [fetchVehicles]);

  useEffect(() => {
    // Update timestamp when vehicles are successfully loaded
    if (vehiclesLoading === 'success' && vehicles.length > 0) {
      setLastLoadedTime(new Date());
    }
  }, [vehiclesLoading, vehicles.length]);

  const handleRefresh = () => {
    fetchVehicles();
  };

  const handleVehicleClick = (vehicleId: string) => {
    onVehicleSelect(vehicleId);
  };

  if (vehiclesLoading === 'loading') {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="body2" sx={{ mt: 2 }}>
          Loading vehicles...
        </Typography>
      </Paper>
    );
  }

  if (vehiclesLoading === 'error') {
    return (
      <Paper sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to load vehicles
        </Alert>
        <Button 
          variant="outlined" 
          startIcon={<RefreshIcon />} 
          onClick={handleRefresh}
        >
          Retry
        </Button>
      </Paper>
    );
  }

  return (
    <Paper sx={{ mb: 2 }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h6">
              Vehicle List
            </Typography>
            {lastLoadedTime && (
              <Typography variant="caption" color="text.secondary">
                Last updated: {lastLoadedTime.toLocaleTimeString()}
              </Typography>
            )}
          </Box>
          <Button
            variant="outlined"
            size="small"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={vehiclesLoading === 'idle'}
          >
            Refresh List
          </Button>
        </Box>
      </Box>

      {vehicles.length === 0 ? (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            No vehicles available
          </Typography>
        </Box>
      ) : (
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Vehicle ID</TableCell>
                <TableCell>Trip ID</TableCell>
                <TableCell>Status</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {vehicles.map((vehicle) => (
                <TableRow 
                  key={vehicle.id}
                  selected={selectedVehicleId === vehicle.id}
                  hover
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleVehicleClick(vehicle.id)}
                >
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {vehicle.id}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {vehicle.tripId}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={vehicle.status}
                      color={vehicle.status === 'active' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Paper>
  );
};

export default VehicleSelector; 