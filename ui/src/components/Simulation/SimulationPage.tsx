import React, { useEffect } from 'react';
import {
  Container,
  Typography,
  Paper,
  Box,
  Chip,
} from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';
import CompactControls from './CompactControls';
import StopTimeline from './StopTimeline';
import SimulationState from './SimulationState';

const SimulationPage: React.FC = () => {
  const { 
    fetchState, 
    loading, 
    startAutoRefresh, 
    stopAutoRefresh 
  } = useSimulationStore();

  useEffect(() => {
    // Fetch initial simulation state when page loads
    fetchState();
    
    // Start auto-refresh
    startAutoRefresh();

    // Cleanup on unmount
    return () => {
      stopAutoRefresh();
    };
  }, []); // Empty dependency array - only run once on mount

  return (
    <Container maxWidth="xl">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h4" component="h1">
          Simulation Control & State
        </Typography>
        <Chip
          icon={<RefreshIcon />}
          label="Auto-refresh: 2s"
          color="primary"
          variant="outlined"
          size="small"
          sx={{ opacity: 0.7 }}
        />
      </Box>
      
      {/* Compact Controls at Top */}
      <CompactControls />
      <SimulationState />
      
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* Stop Timeline - Full Width */}
        <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
          <Box sx={{ flex: '1 1 100%', minWidth: '300px' }}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" gutterBottom>
                Stop Timeline
              </Typography>
              <StopTimeline />
            </Paper>
          </Box>
        </Box>
      </Box>

      {loading === 'loading' && (
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Loading simulation state...
          </Typography>
        </Box>
      )}
    </Container>
  );
};

export default SimulationPage; 