import React from 'react';
import {
  Box,
  Typography,
  Button,
  ButtonGroup,
  Chip,
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  DirectionsBus,
  LocationOn,
} from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';

const NavigationControls: React.FC = () => {
  const { 
    journey,
    nextState, 
    previousState, 
    nextStop, 
    previousStop 
  } = useSimulationStore();

  const currentCallIndex = journey?.currentCallIndex ?? 0;
  const totalCalls = journey?.calls?.length ?? 0;
  const currentCallNumber = currentCallIndex + 1;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      {/* State Navigation */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          State Navigation
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <DirectionsBus color="primary" />
          <Typography variant="body1">
            Current State: 
          </Typography>
          <Chip 
            label={`${currentCallNumber}/${totalCalls}`}
            color="primary"
            variant="outlined"
            size="small"
          />
        </Box>
        <ButtonGroup size="medium" fullWidth>
          <Button
            variant="outlined"
            startIcon={<ArrowBack />}
            onClick={previousState}
            disabled={currentCallIndex <= 0}
          >
            Previous
          </Button>
          <Button
            variant="outlined"
            endIcon={<ArrowForward />}
            onClick={nextState}
            disabled={currentCallIndex >= totalCalls - 1}
          >
            Next
          </Button>
        </ButtonGroup>
      </Box>

      {/* Stop Navigation */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Stop Navigation
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
          <LocationOn color="secondary" />
          <Typography variant="body1">
            Current Stop:
          </Typography>
          <Chip 
            label={`${currentCallNumber}/${totalCalls}`}
            color="secondary"
            variant="outlined"
            size="small"
          />
        </Box>
        <ButtonGroup size="medium" fullWidth>
          <Button
            variant="outlined"
            color="secondary"
            startIcon={<ArrowBack />}
            onClick={previousStop}
            disabled={currentCallIndex <= 0}
          >
            Previous
          </Button>
          <Button
            variant="outlined"
            color="secondary"
            endIcon={<ArrowForward />}
            onClick={nextStop}
            disabled={currentCallIndex >= totalCalls - 1}
          >
            Next
          </Button>
        </ButtonGroup>
      </Box>

      {/* Journey Info */}
      {journey && (
        <Box sx={{ pt: 1, borderTop: 1, borderColor: 'divider' }}>
          <Typography variant="caption" color="text.secondary">
            Trip: {journey.tripId}
          </Typography>
          <br />
          <Typography variant="caption" color="text.secondary">
            Line: {journey.lineName} → {journey.destinationText}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default NavigationControls; 