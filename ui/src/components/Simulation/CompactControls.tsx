import React, { useMemo } from 'react';
import {
  Box,
  IconButton,
  Typography,
  Paper,
  Tooltip,
  LinearProgress,
  Chip,
} from '@mui/material';
import {
  SkipPrevious,
  Remove,
  PlayArrow,
  Add,
  SkipNext,
} from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';
import { 
  calculateJourneyTimeBasedProgress,
  getAbsoluteJourneyStartTime,
  getAbsoluteJourneyEndTime,
  formatTimestamp
} from '../../utils/formatters';

const CompactControls: React.FC = () => {
  const { 
    journey,
    currentCallIndex,
    status,
    playbackState,
    speedMultiplier,
    timestamp,
    stop,
    resume,
    nextState,
    previousState,
    nextStop,
    previousStop
  } = useSimulationStore();

  const totalCalls = journey?.calls?.length ?? 0;
  const isPlaying = playbackState === 'playing';

  // Calculate button enable states according to user requirements
  const canGoBack = (currentCallIndex ?? 0) > 0;
  const canStepBack = (currentCallIndex ?? 0) > 0 || (status && !['BetweenStops', 'AtStop', 'AfterStop'].includes(status));
  const canGoForward = (currentCallIndex ?? 0) < totalCalls - 1;
  const canStepForward = (currentCallIndex ?? 0) < totalCalls - 1;

  const formattedTime = useMemo(() => {
    if (!timestamp) return '00:00:00';
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('en-GB', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }, [timestamp]);

  // Calculate current stop index for display
  const stopInfo = useMemo(() => {
    if (!journey?.calls || journey.calls.length === 0) {
      return null;
    }
    
    const totalStops = journey.calls.length;
    const currentStop = Math.min((currentCallIndex ?? 0) + 1, totalStops); // 1-based index
    
    return {
      current: currentStop,
      total: totalStops
    };
  }, [journey?.calls, currentCallIndex]);

  // Calculate time-based progress (throttled to reduce memory pressure)
  const timeProgress = useMemo(() => {
    if (!journey || !timestamp) return 0;
    // Only recalculate if timestamp changed by more than 5 seconds to reduce computation frequency
    const throttledTimestamp = Math.floor(timestamp / 5) * 5;
    return calculateJourneyTimeBasedProgress(journey, throttledTimestamp);
  }, [journey, timestamp]);

  // Get trip time bounds for display
  const tripTimeBounds = useMemo(() => {
    if (!journey) return null;
    return {
      start: getAbsoluteJourneyStartTime(journey),
      end: getAbsoluteJourneyEndTime(journey)
    };
  }, [journey]);

  const handleSpeedPlay = (speed: number) => {
    resume(speed);
  };

  return (
    <Paper 
      elevation={3} 
      sx={{ 
        p: 2, 
        mb: 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 1,
        bgcolor: 'background.paper',
        borderRadius: 2,
      }}
    >
      {/* Control Buttons Row */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 0.5,
        '& .MuiIconButton-root': {
          minWidth: '48px',
          height: '48px',
          border: '1px solid',
          borderColor: 'divider',
          '&:hover': {
            bgcolor: 'action.hover',
          }
        }
      }}>
        {/* Previous Stop */}
        <Tooltip title="Previous Stop">
          <IconButton
            onClick={previousStop}
            disabled={!canGoBack}
            sx={{ 
              borderRadius: '8px',
              '&.Mui-disabled': { opacity: 0.3 }
            }}
          >
            <SkipPrevious />
          </IconButton>
        </Tooltip>

        {/* Previous State */}
        <Tooltip title="Previous State">
          <IconButton
            onClick={previousState}
            disabled={!canStepBack}
            sx={{ 
              borderRadius: '8px',
              '&.Mui-disabled': { opacity: 0.3 }
            }}
          >
            <Remove />
          </IconButton>
        </Tooltip>

        {/* Play 1x */}
        <Tooltip title="Play 1x Speed">
          <IconButton
            onClick={() => handleSpeedPlay(1)}
            sx={{ 
              borderRadius: '8px',
              bgcolor: speedMultiplier === 1 && isPlaying ? 'success.main' : 'transparent',
              color: speedMultiplier === 1 && isPlaying ? 'success.contrastText' : 'inherit',
              '&:hover': {
                bgcolor: speedMultiplier === 1 && isPlaying ? 'success.dark' : 'action.hover',
              }
            }}
          >
            <PlayArrow />
          </IconButton>
        </Tooltip>

        {/* Play 2x */}
        <Tooltip title="Play 2x Speed">
          <IconButton
            onClick={() => handleSpeedPlay(2)}
            sx={{ 
              borderRadius: '8px',
              bgcolor: speedMultiplier === 2 && isPlaying ? 'success.main' : 'transparent',
              color: speedMultiplier === 2 && isPlaying ? 'success.contrastText' : 'inherit',
              '&:hover': {
                bgcolor: speedMultiplier === 2 && isPlaying ? 'success.dark' : 'action.hover',
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PlayArrow sx={{ fontSize: '16px' }} />
              <Typography variant="caption" sx={{ fontSize: '10px', ml: 0.2 }}>2x</Typography>
            </Box>
          </IconButton>
        </Tooltip>

        {/* Play 4x */}
        <Tooltip title="Play 4x Speed">
          <IconButton
            onClick={() => handleSpeedPlay(4)}
            sx={{ 
              borderRadius: '8px',
              bgcolor: speedMultiplier === 4 && isPlaying ? 'success.main' : 'transparent',
              color: speedMultiplier === 4 && isPlaying ? 'success.contrastText' : 'inherit',
              '&:hover': {
                bgcolor: speedMultiplier === 4 && isPlaying ? 'success.dark' : 'action.hover',
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PlayArrow sx={{ fontSize: '16px' }} />
              <Typography variant="caption" sx={{ fontSize: '10px', ml: 0.2 }}>4x</Typography>
            </Box>
          </IconButton>
        </Tooltip>

        {/* Stop */}
        <Tooltip title="Stop">
          <IconButton
            onClick={stop}
            sx={{ 
              borderRadius: '8px',
              bgcolor: !isPlaying ? 'error.main' : 'transparent',
              color: !isPlaying ? 'error.contrastText' : 'inherit',
              '&:hover': {
                bgcolor: !isPlaying ? 'error.dark' : 'error.light',
              }
            }}
          >
            <Box sx={{ 
              width: '14px', 
              height: '14px', 
              bgcolor: 'currentColor',
              borderRadius: '2px'
            }} />
          </IconButton>
        </Tooltip>

        {/* Next State */}
        <Tooltip title="Next State">
          <IconButton
            onClick={nextState}
            disabled={!canStepForward}
            sx={{ 
              borderRadius: '8px',
              '&.Mui-disabled': { opacity: 0.3 }
            }}
          >
            <Add />
          </IconButton>
        </Tooltip>

        {/* Next Stop */}
        <Tooltip title="Next Stop">
          <IconButton
            onClick={nextStop}
            disabled={!canGoForward}
            sx={{ 
              borderRadius: '8px',
              '&.Mui-disabled': { opacity: 0.3 }
            }}
          >
            <SkipNext />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Time Display with Stop Info */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 2,
        mt: 0.5,
      }}>
        <Typography 
          variant="h6" 
          sx={{ 
            fontFamily: 'monospace',
            fontWeight: 'bold',
            color: 'primary.main',
          }}
        >
          {formattedTime}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {stopInfo && (
            <Chip
              label={`Stop ${stopInfo.current} / ${stopInfo.total}`}
              variant="outlined"
              size="small"
              sx={{ 
                color: 'text.secondary',
                fontWeight: 'medium',
                fontSize: '0.75rem',
                height: '24px',
              }}
            />
          )}
          
          {status && (
            <Chip
              label={status}
              size="small"
              sx={{ 
                fontSize: '0.75rem',
                height: '24px',
                fontWeight: 'medium',
                ...(status === 'AtStop' && {
                  bgcolor: 'success.main',
                  color: 'success.contrastText',
                }),
                ...(status === 'BetweenStops' && {
                  bgcolor: 'info.main',
                  color: 'info.contrastText',
                }),
                ...(status === 'BeforeStop' && {
                  bgcolor: 'warning.main',
                  color: 'warning.contrastText',
                }),
                ...(status === 'AfterStop' && {
                  bgcolor: 'secondary.main',
                  color: 'secondary.contrastText',
                }),
              }}
            />
          )}
        </Box>
      </Box>

      {/* Time-based Progress Bar */}
      {journey && tripTimeBounds?.start && tripTimeBounds?.end && (
        <Box sx={{ width: '100%', mt: 1.5 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
            <Typography variant="caption" color="text.secondary">
              Trip Progress
            </Typography>
            <Chip 
              label={`${Math.round(timeProgress)}%`}
              color="primary"
              size="small"
              sx={{ height: '18px', fontSize: '0.65rem' }}
            />
          </Box>
          
          <LinearProgress 
            variant="determinate" 
            value={timeProgress}
            sx={{ 
              height: 6, 
              borderRadius: 3,
              bgcolor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                borderRadius: 3,
              }
            }}
          />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
              {formatTimestamp(tripTimeBounds.start)}
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.65rem' }}>
              {formatTimestamp(tripTimeBounds.end)}
            </Typography>
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default CompactControls; 