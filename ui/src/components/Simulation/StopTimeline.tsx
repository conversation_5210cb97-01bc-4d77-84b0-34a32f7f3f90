import React from 'react';
import {
  Box,
  Typography,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  IconButton,
  Chip,
  Tooltip,
  Paper,
} from '@mui/material';
import {
  PlayArrow,
  CheckCircle,
  RadioButtonChecked,
  RadioButtonUnchecked,
  Cancel,
} from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';
import { formatTimeWithDelay } from '../../utils/formatters';
import { JourneyCall } from '../../services/types';

const StopTimeline: React.FC = () => {
  const { journey, currentCallIndex, setTime } = useSimulationStore();

  if (!journey || !journey.calls.length) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body2" color="text.secondary">
          No stop information available
        </Typography>
      </Box>
    );
  }

  const getStopStatus = (callIndex: number) => {
    if (callIndex < (currentCallIndex || 0)) {
      return {
        icon: <CheckCircle color="success" fontSize="small" />,
        status: 'completed',
        color: 'success' as const,
      };
    } else if (callIndex === currentCallIndex) {
      return {
        icon: <RadioButtonChecked color="primary" fontSize="small" />,
        status: 'current',
        color: 'primary' as const,
      };
    } else {
      return {
        icon: <RadioButtonUnchecked color="disabled" fontSize="small" />,
        status: 'upcoming',
        color: 'default' as const,
      };
    }
  };

  const getJumpTime = (call: JourneyCall): number | null => {
    // Prefer actual arrival over scheduled arrival
    return call.actualArrivalTime || call.scheduledArrivalTime || null;
  };

  const handleJumpToStop = (call: JourneyCall) => {
    const jumpTime = getJumpTime(call);
    if (jumpTime) {
      setTime(jumpTime);
    }
  };

  const isCurrentStop = (index: number) => index === currentCallIndex;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        All Stops ({journey.calls.length})
      </Typography>
      
      <Paper 
        variant="outlined" 
        sx={{ 
          flex: 1, 
          overflow: 'hidden',
          bgcolor: 'transparent',
        }}
      >
        <Box sx={{ maxHeight: '400px', overflow: 'auto' }}>
          <Table size="small" stickyHeader sx={{ bgcolor: 'transparent' }}>
                         <TableHead>
               <TableRow sx={{ bgcolor: 'transparent' }}>
                 <TableCell sx={{ width: '60px', fontWeight: 'bold', bgcolor: 'transparent', borderBottom: '1px solid', borderColor: 'divider' }}>Stop</TableCell>
                 <TableCell sx={{ width: '60px', fontWeight: 'bold', bgcolor: 'transparent', borderBottom: '1px solid', borderColor: 'divider' }}>Seq</TableCell>
                 <TableCell sx={{ fontWeight: 'bold', bgcolor: 'transparent', borderBottom: '1px solid', borderColor: 'divider' }}>Stop Name</TableCell>
                 <TableCell sx={{ width: '120px', fontWeight: 'bold', bgcolor: 'transparent', borderBottom: '1px solid', borderColor: 'divider' }}>Arrival</TableCell>
                 <TableCell sx={{ width: '120px', fontWeight: 'bold', bgcolor: 'transparent', borderBottom: '1px solid', borderColor: 'divider' }}>Departure</TableCell>
                 <TableCell sx={{ width: '80px', fontWeight: 'bold', bgcolor: 'transparent', borderBottom: '1px solid', borderColor: 'divider' }}>Action</TableCell>
               </TableRow>
             </TableHead>
            <TableBody>
              {journey.calls.map((call, index) => {
                const stopStatus = getStopStatus(index);
                const isCurrent = isCurrentStop(index);
                const jumpTime = getJumpTime(call);
                
                return (
                                     <TableRow
                     key={`${call.stopId}-${index}`}
                     sx={{
                       bgcolor: isCurrent ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                       borderLeft: isCurrent ? 3 : 0,
                       borderColor: 'primary.main',
                       '&:hover': {
                         bgcolor: isCurrent ? 'rgba(25, 118, 210, 0.12)' : 'rgba(255, 255, 255, 0.04)',
                       },
                     }}
                  >
                                         <TableCell sx={{ bgcolor: 'transparent' }}>
                       <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                         {stopStatus.icon}
                         <Typography variant="body2" sx={{ fontWeight: isCurrent ? 'medium' : 'normal' }}>
                           {index + 1}
                         </Typography>
                       </Box>
                     </TableCell>
                     
                     <TableCell sx={{ bgcolor: 'transparent' }}>
                       <Typography variant="body2" color="text.secondary">
                         {call.seqNumber}
                       </Typography>
                     </TableCell>
                     
                     <TableCell sx={{ bgcolor: 'transparent' }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography 
                            variant="body2" 
                            sx={{ 
                              fontWeight: isCurrent ? 'medium' : 'normal',
                              color: isCurrent ? 'primary.main' : 'text.primary',
                            }}
                          >
                            {call.stopName}
                          </Typography>
                          {isCurrent && (
                            <Chip 
                              label="Current" 
                              color="primary" 
                              size="small" 
                              variant="filled"
                            />
                          )}
                          {call.isCancelled && (
                            <Chip 
                              label="Cancelled" 
                              color="error" 
                              size="small" 
                              variant="outlined"
                              icon={<Cancel fontSize="small" />}
                            />
                          )}
                        </Box>
                        {call.platformName && (
                          <Typography variant="caption" color="text.secondary">
                            Platform {call.platformName}
                          </Typography>
                        )}
                      </Box>
                                         </TableCell>
                     
                     <TableCell sx={{ bgcolor: 'transparent' }}>
                       <Typography variant="body2" color="text.secondary">
                         {call.actualArrivalTime || call.scheduledArrivalTime ? 
                           formatTimeWithDelay(call.actualArrivalTime, call.scheduledArrivalTime ?? undefined) : 
                           'n/a'
                         }
                       </Typography>
                     </TableCell>
                     
                     <TableCell sx={{ bgcolor: 'transparent' }}>
                       <Typography variant="body2" color="text.secondary">
                         {call.actualDepartureTime || call.scheduledDepartureTime ? 
                           formatTimeWithDelay(call.actualDepartureTime, call.scheduledDepartureTime ?? undefined) : 
                           'n/a'
                         }
                       </Typography>
                     </TableCell>
                     
                     <TableCell sx={{ bgcolor: 'transparent' }}>
                      <Tooltip title={jumpTime ? "Jump to arrival time" : "No arrival time available"}>
                        <span>
                          <IconButton
                            size="small"
                            disabled={!jumpTime}
                            onClick={() => handleJumpToStop(call)}
                            color={isCurrent ? "primary" : "default"}
                            sx={{
                              '&:hover': {
                                bgcolor: isCurrent ? 'primary.100' : 'action.hover',
                              },
                            }}
                          >
                            <PlayArrow fontSize="small" />
                          </IconButton>
                        </span>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </Box>
      </Paper>
    </Box>
  );
};

export default StopTimeline; 