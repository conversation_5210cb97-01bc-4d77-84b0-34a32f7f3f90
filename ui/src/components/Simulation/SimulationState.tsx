import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  Divider,
} from '@mui/material';
import {
  LocationOn,
  Schedule,
  DirectionsBus,
  Info,
} from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';
import { formatTime, formatDelayInfo } from '../../utils/formatters';

const SimulationState: React.FC = () => {
  const { journey, timestamp } = useSimulationStore();

  if (!journey || !journey.calls.length) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body2" color="text.secondary">
          No simulation data available
        </Typography>
      </Box>
    );
  }

  const currentCallIndex = journey.currentCallIndex ?? 0;
  const currentCall = journey.calls[currentCallIndex];
  const nextCall = journey.calls[currentCallIndex + 1];

  if (!currentCall) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body2" color="text.secondary">
          No current stop information
        </Typography>
      </Box>
    );
  }

  const getLocationStatus = () => {
    const now = timestamp;
    const scheduledArrival = currentCall.scheduledArrivalTime;
    const scheduledDeparture = currentCall.scheduledDepartureTime;
    const actualArrival = currentCall.actualArrivalTime;
    const actualDeparture = currentCall.actualDepartureTime;

    if (actualDeparture && now >= actualDeparture) {
      return { status: 'Departed', color: 'info' as const };
    } else if (actualArrival && now >= actualArrival) {
      return { status: 'At Stop', color: 'success' as const };
    } else if (scheduledArrival && scheduledDeparture && now >= scheduledArrival && now < scheduledDeparture) {
      return { status: 'At Stop', color: 'success' as const };
    } else if (scheduledArrival && now < scheduledArrival) {
      return { status: 'Approaching', color: 'warning' as const };
    } else {
      return { status: 'At Stop', color: 'success' as const };
    }
  };

  const locationStatus = getLocationStatus();

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Current Stop */}
      <Card variant="outlined">
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 2 }}>
            <LocationOn color="primary" sx={{ mt: 0.5 }} />
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" component="div">
                {currentCall.stopName}
              </Typography>
              {currentCall.platformName && (
                <Typography variant="body2" color="text.secondary">
                  Platform {currentCall.platformName}
                </Typography>
              )}
            </Box>
            <Chip
              label={locationStatus.status}
              color={locationStatus.color}
              size="small"
              variant="filled"
            />
          </Box>

          {/* Timing Information */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Schedule fontSize="small" color="action" />
              <Typography variant="body2">
                Arrival: {
                  (currentCall.actualArrivalTime || currentCall.scheduledArrivalTime) 
                    ? formatTime(currentCall.actualArrivalTime || currentCall.scheduledArrivalTime!)
                    : 'N/A'
                }
                {currentCall.actualArrivalTime && currentCall.scheduledArrivalTime && (
                  <span style={{ color: '#666', marginLeft: 4 }}>
                    {formatDelayInfo(currentCall.actualArrivalTime, currentCall.scheduledArrivalTime)}
                  </span>
                )}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Schedule fontSize="small" color="action" />
              <Typography variant="body2">
                Departure: {
                  (currentCall.actualDepartureTime || currentCall.scheduledDepartureTime) 
                    ? formatTime(currentCall.actualDepartureTime || currentCall.scheduledDepartureTime!)
                    : 'N/A'
                }
                {currentCall.actualDepartureTime && currentCall.scheduledDepartureTime && (
                  <span style={{ color: '#666', marginLeft: 4 }}>
                    {formatDelayInfo(currentCall.actualDepartureTime, currentCall.scheduledDepartureTime)}
                  </span>
                )}
              </Typography>
            </Box>
          </Box>

          {/* Advisory Messages */}
          {currentCall.advisoryMessages && currentCall.advisoryMessages.length > 0 && (
            <>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <Info fontSize="small" color="info" sx={{ mt: 0.5 }} />
                <Box>
                  {currentCall.advisoryMessages.map((message, index) => (
                    <Typography key={index} variant="caption" display="block" color="info.main">
                      {message}
                    </Typography>
                  ))}
                </Box>
              </Box>
            </>
          )}
        </CardContent>
      </Card>

      {/* Next Stop Preview */}
      {nextCall && (
        <Box>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Next Stop
          </Typography>
          <Card variant="outlined" sx={{ bgcolor: 'transparent' }}>
            <CardContent sx={{ py: 1.5 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <DirectionsBus fontSize="small" color="action" />
                <Typography variant="body2">
                  {nextCall.stopName}
                </Typography>
                {nextCall.platformName && (
                  <Typography variant="caption" color="text.secondary">
                    (Platform {nextCall.platformName})
                  </Typography>
                )}
              </Box>
              <Typography variant="caption" color="text.secondary">
                Scheduled arrival: {nextCall.scheduledArrivalTime ? formatTime(nextCall.scheduledArrivalTime) : 'N/A'}
              </Typography>
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default SimulationState; 