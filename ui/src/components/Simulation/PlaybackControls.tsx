import React from 'react';
import {
  Box,
  Button,
  Typography,
  FormControl,
  Select,
  MenuItem,
  Chip,
  ButtonGroup,
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  Stop,
  Speed,
} from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';

const PlaybackControls: React.FC = () => {
  const { 
        playbackState,
    speedMultiplier, 
    stop, 
    resume 
  } = useSimulationStore();

  const handleSpeedChange = (newSpeed: number) => {
    if (playbackState === 'playing') {
      resume(newSpeed);
    }
  };

  const getStatusColor = () => {
    switch (playbackState) {
      case 'playing':
        return 'success';
      case 'stopped':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusIcon = () => {
    switch (playbackState) {
      case 'playing':
        return <PlayArrow />;
      case 'stopped':
        return <Stop />;
      default:
        return <Pause />;
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Status Display */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Current Status
        </Typography>
        <Chip
          icon={getStatusIcon()}
          label={playbackState === 'playing' ? 'Playing' : 'Stopped'}
          color={getStatusColor()}
          variant="outlined"
          size="medium"
        />
      </Box>

      {/* Speed Control */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Playback Speed
        </Typography>
        <FormControl size="small" sx={{ minWidth: 120 }}>
          <Select
            value={speedMultiplier}
            onChange={(e) => handleSpeedChange(Number(e.target.value))}
            startAdornment={<Speed sx={{ mr: 1, color: 'text.secondary' }} />}
          >
            <MenuItem value={1}>1x</MenuItem>
            <MenuItem value={2}>2x</MenuItem>
            <MenuItem value={4}>4x</MenuItem>
            <MenuItem value={8}>8x</MenuItem>
            <MenuItem value={10}>10x</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Control Buttons */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom sx={{ mb: 1 }}>
          Controls
        </Typography>
        <ButtonGroup orientation="vertical" size="medium" fullWidth>
          {playbackState === 'stopped' ? (
            <Button
              variant="contained"
              color="success"
              startIcon={<PlayArrow />}
              onClick={() => resume(speedMultiplier)}
            >
              Resume
            </Button>
          ) : (
            <Button
              variant="contained"
              color="warning"
              startIcon={<Pause />}
              onClick={stop}
            >
              Pause
            </Button>
          )}
          
          <Button
            variant="outlined"
            color="error"
            startIcon={<Stop />}
            onClick={stop}
          >
            Stop
          </Button>
        </ButtonGroup>
      </Box>
    </Box>
  );
};

export default PlaybackControls; 