import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
} from '@mui/material';
import {
  AccessTime,
  FlashOn,
} from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';

const TimeControls: React.FC = () => {
  const { timestamp, setTime } = useSimulationStore();
  const [timeInput, setTimeInput] = useState('');

  const formattedTime = useMemo(() => {
    if (!timestamp) return '00:00:00';
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString('en-GB', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }, [timestamp]);

  const parseTimeInput = (timeStr: string): number | null => {
    const timeRegex = /^(\d{1,2}):(\d{2}):(\d{2})$/;
    const match = timeStr.match(timeRegex);
    
    if (!match) return null;
    
    const hours = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    const seconds = parseInt(match[3], 10);
    
    if (hours > 23 || minutes > 59 || seconds > 59) return null;
    
    // Create timestamp for today with the specified time
    const today = new Date();
    today.setHours(hours, minutes, seconds, 0);
    return Math.floor(today.getTime() / 1000);
  };

  const handleSetTime = () => {
    const parsedTime = parseTimeInput(timeInput);
    if (parsedTime !== null) {
      setTime(parsedTime);
      setTimeInput('');
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSetTime();
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
      {/* Current Time Display */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Current Time
        </Typography>
        <Paper 
          elevation={1} 
          sx={{ 
            p: 2, 
            bgcolor: 'grey.50',
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          <AccessTime color="primary" />
          <Typography 
            variant="h4" 
            component="div" 
            sx={{ 
              fontFamily: 'monospace',
              fontWeight: 'bold',
              color: 'primary.main',
            }}
          >
            {formattedTime}
          </Typography>
        </Paper>
      </Box>

      {/* Manual Time Setting */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Set Time
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            size="small"
            placeholder="HH:MM:SS"
            value={timeInput}
            onChange={(e) => setTimeInput(e.target.value)}
            onKeyPress={handleKeyPress}
            sx={{ fontFamily: 'monospace' }}
            helperText="24-hour format"
          />
          <Button
            variant="contained"
            size="small"
            startIcon={<FlashOn />}
            onClick={handleSetTime}
            disabled={!timeInput.trim()}
          >
            Set
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default TimeControls; 