import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import {
  DirectionsBus,
  Route,
  Flag,
  CalendarToday,
  AccessTime,
  FlightTakeoff,
} from '@mui/icons-material';
import { useSimulationStore } from '../../store/simulationStore';
import { 
  formatDate, 
  formatTimestamp, 
  getOperationDay, 
  getTripStartTime, 
  getTripEndTime 
} from '../../utils/formatters';

const JourneyProgress: React.FC = () => {
  const { journey, currentCallIndex, status } = useSimulationStore();

  if (!journey) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="body2" color="text.secondary">
          No journey loaded
        </Typography>
      </Box>
    );
  }

  const totalCalls = journey.calls.length;
  const progressPercentage = totalCalls > 1 ? ((currentCallIndex ?? 0) / (totalCalls - 1)) * 100 : 0;

  // Calculate trip timing information
  // Convert JourneyCall to Stop format for utility functions
  const tripForFormatters = {
    tripId: journey.tripId,
    destinationText: journey.destinationText,
    lineName: journey.lineName,
    calls: journey.calls.map(call => ({
      ...call,
      scheduledArrivalTime: call.scheduledArrivalTime ?? 0,
      scheduledDepartureTime: call.scheduledDepartureTime ?? 0
    }))
  };
  
  const operationDay = getOperationDay(tripForFormatters);
  const tripStartTime = getTripStartTime(tripForFormatters);
  const tripEndTime = getTripEndTime(tripForFormatters);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%' }}>
      {/* Trip Information */}
      <Card variant="outlined">
        <CardContent sx={{ pb: '16px !important' }}>
          {/* Trip ID */}
          {journey.tripId && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <DirectionsBus color="primary" />
              <Typography variant="h6" component="div">
                {journey.tripId}
              </Typography>
            </Box>
          )}
          
          {/* Line Name */}
          {journey.lineName && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <Route color="secondary" />
              <Typography variant="body1">
                Line {journey.lineName}
              </Typography>
            </Box>
          )}

          {/* Destination */}
          {journey.destinationText && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Flag color="action" />
              <Typography variant="body2" color="text.secondary">
                → {journey.destinationText}
              </Typography>
            </Box>
          )}

          {/* Fallback if no data */}
          {!journey.tripId && !journey.lineName && !journey.destinationText && (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 1 }}>
              Journey information not available
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Compressed Trip Timing */}
      <Card variant="outlined" sx={{ bgcolor: 'background.default', width: '100%' }}>
        <CardContent sx={{ py: 2, px: 2, '&:last-child': { pb: 2 } }}>
          <Typography variant="body2" fontWeight="medium" sx={{ mb: 1.5, display: 'flex', alignItems: 'center', gap: 1 }}>
            <AccessTime fontSize="small" color="primary" />
            Trip Timing
          </Typography>
          
          {/* Fixed container with proper width constraints */}
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            gap: 1.5,
            width: '100%',
            maxWidth: '100%',
            overflow: 'hidden'
          }}>
            {/* Operation Day */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 0 }}>
              <CalendarToday sx={{ fontSize: 14, flexShrink: 0 }} color="action" />
              <Box sx={{ minWidth: 0, flex: 1 }}>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  Operation Day
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', wordBreak: 'break-word' }}>
                  {operationDay ? formatDate(operationDay) : 'n/a'}
                </Typography>
              </Box>
            </Box>

            {/* Trip Start */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 0 }}>
              <FlightTakeoff sx={{ fontSize: 14, flexShrink: 0 }} color="action" />
              <Box sx={{ minWidth: 0, flex: 1 }}>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  Trip Start
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', fontFamily: 'monospace' }}>
                  {tripStartTime ? formatTimestamp(tripStartTime) : 'n/a'}
                </Typography>
              </Box>
            </Box>

            {/* Trip End */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, minWidth: 0 }}>
              <Flag sx={{ fontSize: 14, flexShrink: 0 }} color="action" />
              <Box sx={{ minWidth: 0, flex: 1 }}>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                  Trip End
                </Typography>
                <Typography variant="body2" sx={{ fontSize: '0.8rem', fontFamily: 'monospace' }}>
                  {tripEndTime ? formatTimestamp(tripEndTime) : 'n/a'}
                </Typography>
              </Box>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Progress Section */}
      <Box>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Journey Progress
          </Typography>
          <Chip 
            label={`${Math.round(progressPercentage)}%`}
            color="primary"
            size="small"
          />
        </Box>
        
        <LinearProgress 
          variant="determinate" 
          value={progressPercentage}
          sx={{ 
            height: 8, 
            borderRadius: 4,
            bgcolor: 'grey.200',
            '& .MuiLinearProgress-bar': {
              borderRadius: 4,
            }
          }}
        />
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
          <Typography variant="caption" color="text.secondary">
            Stop {(currentCallIndex ?? 0) + 1} of {totalCalls}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {totalCalls - (currentCallIndex ?? 0) - 1} remaining
          </Typography>
        </Box>
      </Box>

      {/* Status */}
      <Box>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Status
        </Typography>
        <Chip
          label={status || 'Active'}
          color={status === 'active' ? 'success' : 'default'}
          variant="filled"
          size="small"
        />
      </Box>
    </Box>
  );
};

export default JourneyProgress; 