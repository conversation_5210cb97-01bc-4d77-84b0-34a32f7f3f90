import React from 'react';
import { AppBar, Toolbar, Typography, Box } from '@mui/material';

const Header: React.FC = () => {
  return (
    <AppBar
      position="fixed"
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
      }}
    >
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          ODIS - Onboard Data Server
        </Typography>
        <Box sx={{ flexGrow: 1 }} />
        {/* Future: Add connection status indicators here */}
      </Toolbar>
    </AppBar>
  );
};

export default Header; 