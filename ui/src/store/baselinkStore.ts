import { create } from 'zustand';
import { Vehicle, ConnectionStatus, Trip, LoadingState } from '../services/types';
import { baselinkApi, dataApi } from '../services/api';

interface BaselinkStore {
  // Connection state
  connectionStatus: ConnectionStatus | null;
  
  // Data
  vehicles: Vehicle[];
  selectedVehicle: Vehicle | null;
  currentTrip: Trip | null;
  
  // Loading states
  connectionLoading: LoadingState;
  vehiclesLoading: LoadingState;
  tripLoading: LoadingState;
  
  // Actions
  fetchConnectionStatus: () => Promise<void>;
  fetchVehicles: () => Promise<void>;
  fetchTripById: (tripId: string) => Promise<void>;
  selectVehicle: (vehicle: Vehicle | null) => void;
  loadTripIntoSimulation: () => Promise<boolean>;
}

export const useBaselinkStore = create<BaselinkStore>((set, get) => ({
  // Initial state
  connectionStatus: null,
  vehicles: [],
  selectedVehicle: null,
  currentTrip: null,
  connectionLoading: 'idle',
  vehiclesLoading: 'idle',
  tripLoading: 'idle',

  // Actions
  fetchConnectionStatus: async () => {
    set({ connectionLoading: 'loading' });
    try {
      const response = await baselinkApi.getStatus();
      if (response.success && response.data) {
        set({
          connectionStatus: response.data,
          connectionLoading: 'success',
        });
      } else {
        set({ connectionLoading: 'error' });
      }
    } catch (error) {
      console.error('Failed to fetch connection status:', error);
      set({ connectionLoading: 'error' });
    }
  },

  fetchVehicles: async () => {
    set({ vehiclesLoading: 'loading' });
    try {
      const response = await baselinkApi.getVehicles();
      if (response.success && response.data) {
        set({
          vehicles: response.data,
          vehiclesLoading: 'success',
        });
      } else {
        set({ vehiclesLoading: 'error' });
      }
    } catch (error) {
      console.error('Failed to fetch vehicles:', error);
      set({ vehiclesLoading: 'error' });
    }
  },

  fetchTripById: async (tripId: string) => {
    set({ tripLoading: 'loading' });
    try {
      const response = await baselinkApi.getTripById(tripId);
      if (response.success && response.data) {
        set({
          currentTrip: response.data,
          tripLoading: 'success',
        });
      } else {
        set({ tripLoading: 'error' });
      }
    } catch (error) {
      console.error('Failed to fetch trip:', error);
      set({ tripLoading: 'error' });
    }
  },

  selectVehicle: (vehicle: Vehicle | null) => {
    set({ selectedVehicle: vehicle });
    
    // If vehicle is selected, fetch its trip
    if (vehicle) {
      get().fetchTripById(vehicle.tripId);
    } else {
      set({ currentTrip: null });
    }
  },

  loadTripIntoSimulation: async () => {
    const { currentTrip } = get();
    if (!currentTrip) {
      console.error('No trip to load');
      return false;
    }

    try {
      const response = await dataApi.loadJourney(currentTrip);
      if (response.success) {
        console.log('Trip loaded successfully');
        return true;
      } else {
        console.error('Failed to load trip:', response.message);
        return false;
      }
    } catch (error) {
      console.error('Failed to load trip into simulation:', error);
      return false;
    }
  },
})); 