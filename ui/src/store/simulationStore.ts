import { create } from 'zustand';
import { SimulationState, LoadingState } from '../services/types';
import { simulationApi } from '../services/api';

interface SimulationStore extends SimulationState {
  // Loading states
  loading: LoadingState;
  
  // Auto-refresh state
  isAutoRefreshEnabled: boolean;
  autoRefreshInterval: number | null;
  abortController: AbortController | null;
  
  // Actions
  fetchState: () => Promise<void>;
  fetchStateQuiet: () => Promise<void>; // Fetch without loading state changes
  stop: () => Promise<void>;
  resume: (speedMultiplier?: number) => Promise<void>;
  setTime: (timestamp: number) => Promise<void>;
  nextState: () => Promise<void>;
  previousState: () => Promise<void>;
  nextStop: () => Promise<void>;
  previousStop: () => Promise<void>;
  
  // Auto-refresh controls
  startAutoRefresh: () => void;
  stopAutoRefresh: () => void;
  
  // UI state
  setLoading: (loading: LoadingState) => void;
}

export const useSimulationStore = create<SimulationStore>((set, get) => ({
  // Initial state
  timestamp: 0,
  playbackState: 'stopped',
  speedMultiplier: 4,
  loading: 'idle',
  
  // Auto-refresh state
  isAutoRefreshEnabled: false,
  autoRefreshInterval: null,
  abortController: null,

  // Actions
  fetchState: async () => {
    set({ loading: 'loading' });
    try {
      const response = await simulationApi.getState();
      if (response.success && response.data) {
        set({
          journey: response.data.journey,
          currentCallIndex: response.data.currentCallIndex,
          status: response.data.status,
          timestamp: response.data.timestamp,
          playbackState: response.data.playbackState,
          speedMultiplier: response.data.speedMultiplier,
          loading: 'success',
        });
      } else {
        set({ loading: 'error' });
      }
    } catch (error) {
      console.error('Failed to fetch simulation state:', error);
      set({ loading: 'error' });
    }
  },

  stop: async () => {
    try {
      const response = await simulationApi.stop();
      if (response.success) {
        // Don't set local state - let auto-refresh handle it
        // This prevents race conditions with the auto-refresh mechanism
        get().fetchStateQuiet();
      }
    } catch (error) {
      console.error('Failed to stop simulation:', error);
    }
  },

  resume: async (speedMultiplier = 4) => {
    try {
      const response = await simulationApi.resume(speedMultiplier);
      if (response.success) {
        // Don't set local state - let auto-refresh handle it
        // This prevents race conditions with the auto-refresh mechanism
        get().fetchStateQuiet();
      }
    } catch (error) {
      console.error('Failed to resume simulation:', error);
    }
  },

  setTime: async (timestamp: number) => {
    try {
      const response = await simulationApi.setTime(timestamp);
      if (response.success) {
        set({ timestamp });
      }
    } catch (error) {
      console.error('Failed to set time:', error);
    }
  },

  nextState: async () => {
    try {
      await simulationApi.nextState();
      // Use quiet fetch to avoid loading indicators
      get().fetchStateQuiet();
    } catch (error) {
      console.error('Failed to go to next state:', error);
    }
  },

  previousState: async () => {
    try {
      await simulationApi.previousState();
      // Use quiet fetch to avoid loading indicators
      get().fetchStateQuiet();
    } catch (error) {
      console.error('Failed to go to previous state:', error);
    }
  },

  nextStop: async () => {
    try {
      await simulationApi.nextStop();
      // Use quiet fetch to avoid loading indicators
      get().fetchStateQuiet();
    } catch (error) {
      console.error('Failed to go to next stop:', error);
    }
  },

  previousStop: async () => {
    try {
      await simulationApi.previousStop();
      // Use quiet fetch to avoid loading indicators
      get().fetchStateQuiet();
    } catch (error) {
      console.error('Failed to go to previous stop:', error);
    }
  },

  // Quiet fetch for auto-refresh (no loading state changes)
  fetchStateQuiet: async () => {
    const state = get();
    
    // Cancel any pending request
    if (state.abortController) {
      state.abortController.abort();
    }
    
    // Create new abort controller for this request
    const abortController = new AbortController();
    set({ abortController });
    
    try {
      const response = await simulationApi.getState();
      if (response.success && response.data) {
        set({
          journey: response.data.journey,
          currentCallIndex: response.data.currentCallIndex,
          status: response.data.status,
          timestamp: response.data.timestamp,
          playbackState: response.data.playbackState,
          speedMultiplier: response.data.speedMultiplier,
        });
      }
    } catch (error) {
      // Only log error if it's not due to request cancellation
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Failed to fetch simulation state (quiet):', error);
      }
    } finally {
      // Clear the abort controller if it's still the current one
      const currentState = get();
      if (currentState.abortController === abortController) {
        set({ abortController: null });
      }
    }
  },

  // Auto-refresh controls
  startAutoRefresh: () => {
    const state = get();
    if (state.autoRefreshInterval) {
      clearInterval(state.autoRefreshInterval);
    }
    
    const intervalId = setInterval(() => {
      get().fetchStateQuiet();
    }, 2000); // Update every 2 seconds

    set({
      isAutoRefreshEnabled: true,
      autoRefreshInterval: intervalId,
    });
  },

  stopAutoRefresh: () => {
    const state = get();
    if (state.autoRefreshInterval) {
      clearInterval(state.autoRefreshInterval);
    }
    
    // Cancel any pending request
    if (state.abortController) {
      state.abortController.abort();
    }
    
    set({
      isAutoRefreshEnabled: false,
      autoRefreshInterval: null,
      abortController: null,
    });
  },

  setLoading: (loading: LoadingState) => set({ loading }),
})); 