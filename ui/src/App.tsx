// React import not needed with new JSX transform
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from './theme';
import AppLayout from './components/Layout/AppLayout';
import TripLoadingPage from './components/TripLoading/TripLoadingPage';
import SimulationPage from './components/Simulation/SimulationPage';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <Router>
        <Routes>
          <Route path="/" element={<AppLayout />}>
            <Route index element={<Navigate to="/trip-loading" replace />} />
            <Route path="/trip-loading" element={<TripLoadingPage />} />
            <Route path="/simulation" element={<SimulationPage />} />
          </Route>
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;
