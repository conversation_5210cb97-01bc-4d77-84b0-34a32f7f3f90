// Format Unix timestamp to readable time string
export const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleTimeString(undefined, { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// Format Unix timestamp to full date and time
export const formatDateTime = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleString(undefined, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// Format Unix timestamp to date only
export const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Format delay in seconds to human readable string
export const formatDelay = (delaySeconds: number): string => {
  if (Math.abs(delaySeconds) < 30) {
    return 'on time';
  }
  
  const absDelay = Math.abs(delaySeconds);
  const minutes = Math.floor(absDelay / 60);
  const seconds = absDelay % 60;
  
  let result = '';
  if (minutes > 0) {
    result += `${minutes}m`;
  }
  if (seconds > 0) {
    result += ` ${seconds}s`;
  }
  
  return delaySeconds > 0 ? `+${result}` : `-${result}`;
};

// Format trip progress as percentage
export const formatProgress = (currentIndex: number, totalStops: number): number => {
  if (totalStops === 0) return 0;
  return Math.round((currentIndex / totalStops) * 100);
};

// Format speed multiplier display
export const formatSpeedMultiplier = (speed: number): string => {
  return `${speed}x`;
};

// Format connection status
export const formatConnectionStatus = (connected: boolean): { text: string; color: string } => {
  return connected 
    ? { text: 'Connected', color: 'success.main' }
    : { text: 'Disconnected', color: 'error.main' };
};

// ============================================================================
// Trip Analysis Functions
// ============================================================================

import { Trip, Stop, Journey } from '../services/types';

// Calculate operation day from trip data (earliest scheduled time)
export const getOperationDay = (trip: Trip): number | null => {
  if (!trip.calls || trip.calls.length === 0) return null;
  
  const earliestTime = trip.calls.reduce((earliest, call) => {
    const callEarliest = Math.min(
      call.scheduledArrivalTime || Infinity,
      call.scheduledDepartureTime || Infinity
    );
    return Math.min(earliest, callEarliest);
  }, Infinity);
  
  return earliestTime === Infinity ? null : earliestTime;
};

// Get trip start time (prefer actual departure over scheduled, fallback to arrival)
export const getTripStartTime = (trip: Trip): number | null => {
  if (!trip.calls || trip.calls.length === 0) return null;
  
  const firstCall = trip.calls[0];
  return (
    firstCall.actualDepartureTime ||
    firstCall.scheduledDepartureTime ||
    firstCall.actualArrivalTime ||
    firstCall.scheduledArrivalTime ||
    null
  );
};

// Get trip end time (prefer actual arrival over scheduled, fallback to departure)
export const getTripEndTime = (trip: Trip): number | null => {
  if (!trip.calls || trip.calls.length === 0) return null;
  
  const lastCall = trip.calls[trip.calls.length - 1];
  return (
    lastCall.actualArrivalTime ||
    lastCall.scheduledArrivalTime ||
    lastCall.actualDepartureTime ||
    lastCall.scheduledDepartureTime ||
    null
  );
};

// Get absolute trip start time (minimum of all times in all stops)
export const getAbsoluteTripStartTime = (trip: Trip): number | null => {
  if (!trip.calls || trip.calls.length === 0) return null;
  
  let minTime = Infinity;
  
  trip.calls.forEach(call => {
    [call.actualArrivalTime, call.scheduledArrivalTime, call.actualDepartureTime, call.scheduledDepartureTime]
      .forEach(time => {
        if (time && time < minTime) {
          minTime = time;
        }
      });
  });
  
  return minTime === Infinity ? null : minTime;
};

// Get absolute trip end time (maximum of all times in all stops)
export const getAbsoluteTripEndTime = (trip: Trip): number | null => {
  if (!trip.calls || trip.calls.length === 0) return null;
  
  let maxTime = -Infinity;
  
  trip.calls.forEach(call => {
    [call.actualArrivalTime, call.scheduledArrivalTime, call.actualDepartureTime, call.scheduledDepartureTime]
      .forEach(time => {
        if (time && time > maxTime) {
          maxTime = time;
        }
      });
  });
  
  return maxTime === -Infinity ? null : maxTime;
};

// Calculate time-based progress percentage for current timestamp
export const calculateTimeBasedProgress = (trip: Trip, currentTimestamp: number): number => {
  const startTime = getAbsoluteTripStartTime(trip);
  const endTime = getAbsoluteTripEndTime(trip);
  
  if (!startTime || !endTime || startTime >= endTime) {
    return 0;
  }
  
  // Ensure current timestamp is within bounds
  const clampedTimestamp = Math.max(startTime, Math.min(endTime, currentTimestamp));
  
  const progress = ((clampedTimestamp - startTime) / (endTime - startTime)) * 100;
  return Math.max(0, Math.min(100, progress));
};

// Journey-compatible versions (for simulation store)
export const getAbsoluteJourneyStartTime = (journey: Journey): number | null => {
  if (!journey.calls || journey.calls.length === 0) return null;
  
  let minTime = Infinity;
  
  journey.calls.forEach(call => {
    [call.actualArrivalTime, call.scheduledArrivalTime, call.actualDepartureTime, call.scheduledDepartureTime]
      .forEach(time => {
        if (time && time < minTime) {
          minTime = time;
        }
      });
  });
  
  return minTime === Infinity ? null : minTime;
};

export const getAbsoluteJourneyEndTime = (journey: Journey): number | null => {
  if (!journey.calls || journey.calls.length === 0) return null;
  
  let maxTime = -Infinity;
  
  journey.calls.forEach(call => {
    [call.actualArrivalTime, call.scheduledArrivalTime, call.actualDepartureTime, call.scheduledDepartureTime]
      .forEach(time => {
        if (time && time > maxTime) {
          maxTime = time;
        }
      });
  });
  
  return maxTime === -Infinity ? null : maxTime;
};

export const calculateJourneyTimeBasedProgress = (journey: Journey, currentTimestamp: number): number => {
  const startTime = getAbsoluteJourneyStartTime(journey);
  const endTime = getAbsoluteJourneyEndTime(journey);
  
  if (!startTime || !endTime || startTime >= endTime) {
    return 0;
  }
  
  // Ensure current timestamp is within bounds
  const clampedTimestamp = Math.max(startTime, Math.min(endTime, currentTimestamp));
  
  const progress = ((clampedTimestamp - startTime) / (endTime - startTime)) * 100;
  return Math.max(0, Math.min(100, progress));
};

// Get preferred departure time for a stop (actual departure > scheduled departure > actual arrival > scheduled arrival)
export const getPreferredDepartureTime = (stop: Stop): number | null => {
  return (
    stop.actualDepartureTime ||
    stop.scheduledDepartureTime ||
    stop.actualArrivalTime ||
    stop.scheduledArrivalTime ||
    null
  );
};

// Get time with delay information
export const getTimeWithDelay = (actualTime: number | undefined, scheduledTime: number | undefined): {
  time: number | null;
  delay: number | null;
  isActual: boolean;
} => {
  if (actualTime) {
    const delay = scheduledTime ? actualTime - scheduledTime : null;
    return { time: actualTime, delay, isActual: true };
  } else if (scheduledTime) {
    return { time: scheduledTime, delay: null, isActual: false };
  }
  return { time: null, delay: null, isActual: false };
};

// Format time with delay indicator
export const formatTimeWithDelay = (actualTime: number | undefined, scheduledTime: number | undefined): string => {
  const { time, delay, isActual } = getTimeWithDelay(actualTime, scheduledTime);
  
  if (!time) return 'n/a';
  
  const timeStr = formatTimestamp(time);
  
  if (delay && Math.abs(delay) >= 30) {
    const delayStr = formatDelay(delay);
    return `${timeStr} (${delayStr})`;
  }
  
  return isActual ? `${timeStr}` : `${timeStr} (scheduled)`;
};

// Alias for formatTimestamp to match component imports
export const formatTime = formatTimestamp;

// Format delay info between actual and scheduled times
export const formatDelayInfo = (actualTime: number, scheduledTime: number): string => {
  const delay = actualTime - scheduledTime;
  return formatDelay(delay);
}; 