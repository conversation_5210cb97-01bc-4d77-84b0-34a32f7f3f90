// Speed multiplier options for simulation control
export const SPEED_MULTIPLIERS = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10] as const;

// Default speed multiplier
export const DEFAULT_SPEED_MULTIPLIER = 4;

// API refresh intervals (in milliseconds)
export const REFRESH_INTERVALS = {
  CONNECTION_STATUS: 30000, // 30 seconds
  VEHICLES: 60000, // 1 minute
  SIMULATION_STATE: 5000, // 5 seconds (when playing)
} as const;

// UI Layout constants
export const LAYOUT = {
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
} as const;

// Toast notification duration
export const TOAST_DURATION = 4000; // 4 seconds

// Trip loading states
export const TRIP_LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

// Connection states
export const CONNECTION_STATES = {
  CONNECTED: 'connected',
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  ERROR: 'error',
} as const; 