import axios, { AxiosResponse } from 'axios';
import { ApiResponse, Vehicle, ConnectionStatus, Trip, SimulationState, LoadJourneyRequest, Journey } from './types';

// Configure axios instance
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse<unknown>>) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// Baselink API
export const baselinkApi = {
  async getStatus(): Promise<ApiResponse<ConnectionStatus>> {
    const response = await api.get<ApiResponse<ConnectionStatus>>('/baselink/status');
    return response.data;
  },

  async getVehicles(): Promise<ApiResponse<Vehicle[]>> {
    const response = await api.get<ApiResponse<Vehicle[]>>('/baselink/vehicles');
    return response.data;
  },

  async getTripById(tripId: string): Promise<ApiResponse<Trip>> {
    const response = await api.get<ApiResponse<Trip>>(`/baselink/trips/${tripId}`);
    return response.data;
  },
};

// Simulation API
export const simulationApi = {
  async getState(): Promise<ApiResponse<SimulationState>> {
    const response = await api.get<ApiResponse<SimulationState>>('/simulation/state');
    return response.data;
  },

  async stop(): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>('/simulation/stop');
    return response.data;
  },

  async resume(speedMultiplier?: number): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>('/simulation/resume', {
      speed_multiplier: speedMultiplier,
    });
    return response.data;
  },

  async setTime(timestamp: number): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>('/simulation/time', {
      timestamp: timestamp,
    });
    return response.data;
  },

  async nextState(): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>('/simulation/next-state');
    return response.data;
  },

  async previousState(): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>('/simulation/previous-state');
    return response.data;
  },

  async nextStop(): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>('/simulation/next-stop');
    return response.data;
  },

  async previousStop(): Promise<ApiResponse<void>> {
    const response = await api.post<ApiResponse<void>>('/simulation/previous-stop');
    return response.data;
  },
};

// Helper function to convert Trip to Journey format
const convertTripToJourney = (trip: Trip): Journey => ({
  tripId: trip.tripId,  // Backend Journey uses camelCase with serde rename_all
  destinationText: trip.destinationText,
  lineName: trip.lineName,
  calls: trip.calls.map(call => ({
    stopId: call.stopId,  // Backend Call uses camelCase with serde rename_all
    seqNumber: call.seqNumber,
    scheduledArrivalTime: call.scheduledArrivalTime,
    scheduledDepartureTime: call.scheduledDepartureTime,
    actualArrivalTime: call.actualArrivalTime,
    actualDepartureTime: call.actualDepartureTime,
    stopName: call.stopName,
    platformName: call.platformName,
    advisoryMessages: call.advisoryMessages || [],
    isCancelled: call.isCancelled || false,
    exitSide: call.exitSide || 'Unknown',
    destinationText: null,
  }))
});

// Data API
export const dataApi = {
  async loadJourney(trip: Trip): Promise<ApiResponse<void>> {
    const journey = convertTripToJourney(trip);
    const request: LoadJourneyRequest = { journey };
    const response = await api.post<ApiResponse<void>>('/data/load/journey', request);
    return response.data;
  },
};

// Export default api instance for custom requests
export default api; 