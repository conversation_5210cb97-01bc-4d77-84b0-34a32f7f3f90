// API Response Format
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  timestamp: string;
  error?: {
    code: string;
    message: string;
    details?: string;
  };
}

// Baselink Types
export interface Vehicle {
  id: string;
  tripId: string;
  vehicleType: string | null;
  status: string;
}

export interface ConnectionStatus {
  connected: boolean;
  version: string;
  server_url: string;
  startup_time: number;
}

export interface Stop {
  stopId: string;
  seqNumber: number;
  scheduledArrivalTime: number;
  scheduledDepartureTime: number;
  actualArrivalTime?: number;
  actualDepartureTime?: number;
  stopName: string;
  platformName?: string;
  advisoryMessages?: string[];
  isCancelled?: boolean;
  exitSide?: string;
}

export interface Trip {
  tripId: string;
  destinationText: string;
  lineName: string;
  calls: Stop[];
}

// Simulation Types - matching backend camelCase format
export interface JourneyCall {
  stopId: string;
  seqNumber: number;
  scheduledArrivalTime: number | null;
  scheduledDepartureTime: number | null;
  actualArrivalTime?: number;
  actualDepartureTime?: number;
  stopName: string;
  platformName?: string;
  advisoryMessages: string[];
  isCancelled: boolean;
  exitSide: string;
  destinationText?: string | null;
}

export interface Journey {
  tripId: string;  // backend sends camelCase
  destinationText: string;  // backend sends camelCase
  lineName: string;  // backend sends camelCase
  calls: JourneyCall[];
  currentCallIndex?: number;  // Added missing property
}

export interface SimulationState {
  journey?: Journey;
  currentCallIndex?: number;  // backend sends camelCase
  status?: string;
  timestamp: number;
  playbackState: 'playing' | 'stopped';  // backend sends camelCase
  speedMultiplier: number;  // backend sends camelCase
}

// Data Loading Types
export interface LoadJourneyRequest {
  journey: Journey;
}

// UI State Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface UiState {
  loadingStates: Record<string, LoadingState>;
  errorMessages: Record<string, string>;
  toastMessage?: string;
  toastType?: 'success' | 'error' | 'info';
} 