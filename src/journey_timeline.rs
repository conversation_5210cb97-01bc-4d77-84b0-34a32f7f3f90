use crate::onboard_model::{Call, Journey, LocationStatus};

/// Error types for journey timeline operations
#[derive(Debug, PartialEq)]
pub enum TimelineError {
    InsufficientCalls,
    InvalidTimingData,
    InvalidSequenceNumber,
}

impl std::fmt::Display for TimelineError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            TimelineError::InsufficientCalls => {
                write!(f, "Journey has insufficient calls (minimum 2 required)")
            }
            TimelineError::InvalidTimingData => write!(f, "Invalid timing data in journey"),
            TimelineError::InvalidSequenceNumber => {
                write!(f, "Invalid sequence number in journey")
            }
        }
    }
}

impl std::error::Error for TimelineError {}

/// Represents a specific point in time during a journey with its associated state
#[derive(Debug, <PERSON>lone, PartialEq)]
pub struct TimePoint {
    pub timestamp: i64,
    pub status: LocationStatus,
    pub call_index: usize,
}

pub enum TimePrefer {
    Arrival,
    Departure,
}

/// Extension trait for Call to provide timing methods
impl Call {
    pub fn time(&self, prefer: TimePrefer) -> i64 {
        match prefer {
            TimePrefer::Arrival => self.arrival_time().or(self.departure_time()),
            TimePrefer::Departure => self.departure_time().or(self.arrival_time()),
        }
        .expect("No time available")
    }

    /// Gets the effective arrival time, preferring actual over scheduled
    pub fn arrival_time(&self) -> Option<i64> {
        self.actual_arrival_time.or(self.scheduled_arrival_time)
    }

    /// Gets the effective departure time, preferring actual over scheduled
    pub fn departure_time(&self) -> Option<i64> {
        self.actual_departure_time.or(self.scheduled_departure_time)
    }

    /// Checks if arrival has passed based on current timestamp
    pub fn arrival_in_past(&self, now: i64) -> bool {
        let arrival_time = self.time(TimePrefer::Arrival);
        now >= arrival_time
    }

    /// Checks if departure has passed based on current timestamp
    pub fn departure_in_past(&self, now: i64) -> bool {
        let departure_time = self.time(TimePrefer::Departure);
        now >= departure_time
    }
}

/// Pure timeline calculator that handles timepoint generation and navigation
#[derive(Debug)]
pub struct JourneyTimeline {
    timepoints: Vec<TimePoint>,
}

impl JourneyTimeline {
    /// Creates a new JourneyTimeline from a journey
    pub fn from_journey(journey: &Journey) -> Result<Self, TimelineError> {
        validate_journey(journey)?;
        let timepoints = generate_timepoints(journey);
        Ok(Self { timepoints })
    }

    /// Gets the current timepoint for a given timestamp
    pub fn find_current(&self, timestamp: i64) -> TimePoint {
        for timepoint in self.timepoints.iter().rev() {
            if timepoint.timestamp <= timestamp {
                return timepoint.clone();
            }
        }

        self.timepoints.first().expect("No timepoints found").clone()
    }

    /// Gets the next timepoint after the given timestamp
    pub fn find_next_state(&self, timestamp: i64) -> Option<TimePoint> {
        if self.timepoints.len() < 2 {
            return None;
        }

        for timepoint in &self.timepoints {
            if timepoint.timestamp > timestamp {
                return Some(timepoint.clone());
            }
        }

        None
    }

    /// Gets the previous timepoint before the given timestamp
    pub fn find_previous_state(&self, timestamp: i64) -> Option<TimePoint> {
        if self.timepoints.len() < 2 {
            return None;
        }

        let mut current_index = None;
        for (i, timepoint) in self.timepoints.iter().enumerate() {
            if timepoint.timestamp <= timestamp {
                current_index = Some(i);
            } else {
                break;
            }
        }

        if let Some(index) = current_index {
            if index > 0 {
                return Some(self.timepoints[index - 1].clone());
            }
        }

        None
    }

    /// Finds the next stop following the rules:
    /// 1. First try to find exact status match
    /// 2. If no exact match, find closest status by time difference
    /// 3. If no stops ahead, jump to last timepoint
    pub fn find_next_stop(&self, timestamp: i64) -> Option<TimePoint> {
        if self.timepoints.is_empty() {
            return None;
        }

        let current_status = self.find_current(timestamp).status;

        // Step 1: Try to find exact status match ahead
        for timepoint in &self.timepoints {
            if timepoint.timestamp > timestamp && timepoint.status == current_status {
                return Some(timepoint.clone());
            }
        }

        // Step 2: If no exact match, find closest status by time difference
        let mut closest_timepoint = None;
        let mut smallest_diff = i64::MAX;

        for timepoint in &self.timepoints {
            if timepoint.timestamp > timestamp {
                let time_diff = (timepoint.timestamp - timestamp).abs();
                if time_diff < smallest_diff {
                    smallest_diff = time_diff;
                    closest_timepoint = Some(timepoint.clone());
                }
            }
        }

        if let Some(timepoint) = closest_timepoint {
            return Some(timepoint);
        }

        // Step 3: If no stops ahead, jump to last timepoint
        self.timepoints.last().cloned()
    }

    /// Finds the previous stop following the rules:
    /// 1. First try to find exact status match
    /// 2. If no exact match, find closest status by time difference
    /// 3. If no stops behind, jump to first timepoint
    pub fn find_previous_stop(&self, timestamp: i64) -> Option<TimePoint> {
        if self.timepoints.is_empty() {
            return None;
        }

        let current_status = self.find_current(timestamp).status;

        // Step 1: Try to find exact status match behind
        for timepoint in self.timepoints.iter().rev() {
            if timepoint.timestamp < timestamp && timepoint.status == current_status {
                return Some(timepoint.clone());
            }
        }

        // Step 2: If no exact match, find closest status by time difference
        let mut closest_timepoint = None;
        let mut smallest_diff = i64::MAX;

        for timepoint in &self.timepoints {
            if timepoint.timestamp < timestamp {
                let time_diff = (timestamp - timepoint.timestamp).abs();
                if time_diff < smallest_diff {
                    smallest_diff = time_diff;
                    closest_timepoint = Some(timepoint.clone());
                }
            }
        }

        if let Some(timepoint) = closest_timepoint {
            return Some(timepoint);
        }

        // Step 3: If no stops behind, jump to first timepoint
        self.timepoints.first().cloned()
    }

    /// Gets the next timepoint from a given TimePoint
    /// This is more precise than find_next_state as it uses the exact timepoint position
    /// rather than just timestamp, avoiding stuck states when timestamps are identical
    pub fn next_state_from_timepoint(&self, current: &TimePoint) -> Option<TimePoint> {
        // Find the exact current timepoint by matching all fields
        let current_index = self.timepoints.iter().position(|tp| {
            tp.timestamp == current.timestamp 
                && tp.status == current.status 
                && tp.call_index == current.call_index
        })?;

        // Return the next timepoint if it exists
        if current_index + 1 < self.timepoints.len() {
            Some(self.timepoints[current_index + 1].clone())
        } else {
            None
        }
    }

    /// Gets the previous timepoint from a given TimePoint
    /// This is more precise than find_previous_state as it uses the exact timepoint position
    /// rather than just timestamp, avoiding stuck states when timestamps are identical
    pub fn previous_state_from_timepoint(&self, current: &TimePoint) -> Option<TimePoint> {
        // Find the exact current timepoint by matching all fields
        let current_index = self.timepoints.iter().position(|tp| {
            tp.timestamp == current.timestamp 
                && tp.status == current.status 
                && tp.call_index == current.call_index
        })?;

        // Return the previous timepoint if it exists
        if current_index > 0 {
            Some(self.timepoints[current_index - 1].clone())
        } else {
            None
        }
    }

    /// Gets a reference to the timepoints (for debugging/testing)
    pub fn timepoints(&self) -> &[TimePoint] {
        &self.timepoints
    }
}

fn validate_journey(journey: &Journey) -> Result<(), TimelineError> {
    if journey.calls.len() < 2 {
        return Err(TimelineError::InsufficientCalls);
    }
    for (i, call) in journey.calls.iter().enumerate() {
        if i == 0 {
            continue;
        }
        let previous_call = &journey.calls[i - 1];
        if call.arrival_time().is_none() || call.departure_time().is_none() {
            return Err(TimelineError::InvalidTimingData);
        }
        if call.seq_number <= previous_call.seq_number {
            return Err(TimelineError::InvalidSequenceNumber);
        }
        if call.arrival_time() < previous_call.departure_time() {
            return Err(TimelineError::InvalidTimingData);
        }
        if call.arrival_time() > call.departure_time() {
            return Err(TimelineError::InvalidTimingData);
        }
    }
    Ok(())
}

/// Generates all timepoints for a journey
pub fn generate_timepoints(journey: &Journey) -> Vec<TimePoint> {
    if journey.calls.len() < 2 {
        return vec![];
    }

    let mut timepoints = Vec::new();

    // Add special case for before first stop
    let first_call = &journey.calls[0];
    let first_arrival = first_call.time(TimePrefer::Arrival);
    timepoints.push(TimePoint {
        timestamp: first_arrival - 30, // 30 seconds before first arrival
        status: LocationStatus::BeforeStop,
        call_index: 0,
    });

    for (call_index, call) in journey.calls.iter().enumerate() {
        let arrival_time = call.time(TimePrefer::Arrival);
        let departure_time = call.time(TimePrefer::Departure);

        // Add AtStop timepoint
        timepoints.push(TimePoint {
            timestamp: arrival_time,
            status: LocationStatus::AtStop,
            call_index,
        });

        // Add AfterStop timepoint
        timepoints.push(TimePoint {
            timestamp: departure_time,
            status: LocationStatus::AfterStop,
            call_index,
        });

        // Add transition timepoints to next call (if not the last call)
        if call_index < journey.calls.len() - 1 {
            let next_call = &journey.calls[call_index + 1];
            let next_arrival = next_call.time(TimePrefer::Arrival);
            let travel_time = next_arrival - departure_time;

            // BetweenStops starts 5% into travel time
            let travel_time_5_percent = travel_time / 20;
            let between_stops_time = departure_time + travel_time_5_percent;
            timepoints.push(TimePoint {
                timestamp: between_stops_time,
                status: LocationStatus::BetweenStops,
                call_index: call_index + 1, // Traveling TO the next call
            });

            // BeforeStop starts 10% before arrival time
            let travel_time_10_percent = travel_time / 10;
            let before_stop_time = next_arrival - travel_time_10_percent;
            timepoints.push(TimePoint {
                timestamp: before_stop_time,
                status: LocationStatus::BeforeStop,
                call_index: call_index + 1, // Approaching the next call
            });
        }
    }

    // Sort by timestamp to ensure proper ordering
    timepoints.sort_by_key(|tp| tp.timestamp);
    timepoints
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::onboard_model::{ExitSide, Journey};

    fn create_test_call(
        stop_id: &str,
        seq_number: u32,
        scheduled_arrival: i64,
        scheduled_departure: i64,
        actual_arrival: Option<i64>,
        actual_departure: Option<i64>,
    ) -> Call {
        Call {
            stop_id: stop_id.to_string(),
            seq_number,
            scheduled_arrival_time: Some(scheduled_arrival),
            scheduled_departure_time: Some(scheduled_departure),
            actual_arrival_time: actual_arrival,
            actual_departure_time: actual_departure,
            destination_text: Some("Test Destination".to_string()),
            platform_name: Some("Platform 1".to_string()),
            stop_name: format!("Test Stop {}", seq_number),
            advisory_messages: vec![],
            is_cancelled: false,
            exit_side: ExitSide::Unknown,
        }
    }

    fn create_test_journey() -> Journey {
        Journey {
            trip_id: "test-trip".to_string(),
            destination_text: "Test Destination".to_string(),
            line_name: "Test Line".to_string(),
            calls: vec![
                create_test_call("stop1", 1, 1000, 1100, Some(1000), Some(1100)),
                create_test_call("stop2", 2, 1200, 1300, None, None),
                create_test_call("stop3", 3, 1400, 1500, None, None),
            ],
        }
    }

    fn create_zero_dwell_journey() -> Journey {
        Journey {
            trip_id: "zero-dwell-trip".to_string(),
            destination_text: "Zero Dwell Destination".to_string(),
            line_name: "Express Line".to_string(),
            calls: vec![
                create_test_call("stop1", 1, 1000, 1000, Some(1000), Some(1000)), // Zero dwell
                create_test_call("stop2", 2, 1200, 1200, None, None), // Zero dwell
                create_test_call("stop3", 3, 1400, 1400, None, None), // Zero dwell
            ],
        }
    }

    #[test]
    fn test_call_timing_methods() {
        let call = create_test_call("stop1", 1, 1000, 1100, Some(1050), Some(1150));

        // Test timing methods
        assert_eq!(call.time(TimePrefer::Arrival), 1050); // Actual time preferred
        assert_eq!(call.time(TimePrefer::Departure), 1150);

        // Test with no actual times
        let call_no_actual = create_test_call("stop2", 2, 1200, 1300, None, None);
        assert_eq!(call_no_actual.time(TimePrefer::Arrival), 1200); // Falls back to scheduled
        assert_eq!(call_no_actual.time(TimePrefer::Departure), 1300);

        // Test arrival_time and departure_time methods
        assert_eq!(call.arrival_time(), Some(1050));
        assert_eq!(call.departure_time(), Some(1150));
        assert_eq!(call_no_actual.arrival_time(), Some(1200));
        assert_eq!(call_no_actual.departure_time(), Some(1300));
    }

    #[test]
    fn test_call_event_timing() {
        let call = create_test_call("stop1", 1, 1000, 1100, Some(1050), Some(1150));

        // Before arrival
        assert!(!call.arrival_in_past(1000));
        assert!(!call.departure_in_past(1000));

        // After arrival, before departure
        assert!(call.arrival_in_past(1100));
        assert!(!call.departure_in_past(1100));

        // After departure
        assert!(call.arrival_in_past(1200));
        assert!(call.departure_in_past(1200));
    }

    #[test]
    fn test_timeline_creation_from_journey() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();
        
        assert!(!timeline.timepoints.is_empty());
        // Should have multiple timepoints per call plus transitions
        assert!(timeline.timepoints.len() > journey.calls.len());
    }

    #[test]
    fn test_timeline_creation_invalid_journey() {
        // Test with insufficient calls
        let empty_journey = Journey {
            trip_id: "empty".to_string(),
            destination_text: "Empty".to_string(),
            line_name: "Empty Line".to_string(),
            calls: vec![],
        };
        let result = JourneyTimeline::from_journey(&empty_journey);
        assert_eq!(result.unwrap_err(), TimelineError::InsufficientCalls);

        // Test with single call
        let single_call_journey = Journey {
            trip_id: "single".to_string(),
            destination_text: "Single".to_string(),
            line_name: "Single Line".to_string(),
            calls: vec![create_test_call("stop1", 1, 1000, 1100, None, None)],
        };
        let result = JourneyTimeline::from_journey(&single_call_journey);
        assert_eq!(result.unwrap_err(), TimelineError::InsufficientCalls);
    }

    #[test]
    fn test_timepoint_generation() {
        let journey = create_test_journey();
        let timepoints = generate_timepoints(&journey);

        // Verify timepoints are sorted by timestamp
        for i in 1..timepoints.len() {
            assert!(
                timepoints[i].timestamp >= timepoints[i - 1].timestamp,
                "Timepoints should be sorted by timestamp"
            );
        }

        // Verify we have BeforeStop at beginning
        assert_eq!(timepoints[0].status, LocationStatus::BeforeStop);
        assert_eq!(timepoints[0].call_index, 0);

        // Verify we have specific status types
        let has_at_stop = timepoints.iter().any(|tp| tp.status == LocationStatus::AtStop);
        let has_after_stop = timepoints.iter().any(|tp| tp.status == LocationStatus::AfterStop);
        let has_between_stops = timepoints.iter().any(|tp| tp.status == LocationStatus::BetweenStops);
        let has_before_stop = timepoints.iter().any(|tp| tp.status == LocationStatus::BeforeStop);

        assert!(has_at_stop, "Should have AtStop timepoints");
        assert!(has_after_stop, "Should have AfterStop timepoints");
        assert!(has_between_stops, "Should have BetweenStops timepoints");
        assert!(has_before_stop, "Should have BeforeStop timepoints");
    }

    #[test]
    fn test_timepoint_generation_zero_dwell() {
        let journey = create_zero_dwell_journey();
        let timepoints = generate_timepoints(&journey);

        // Should still generate valid timepoints even with zero dwell time
        assert!(!timepoints.is_empty());

        // Verify timepoints are sorted
        for i in 1..timepoints.len() {
            assert!(
                timepoints[i].timestamp >= timepoints[i - 1].timestamp,
                "Timepoints should be sorted even with zero dwell time"
            );
        }

        // For zero dwell time, AtStop and AfterStop should have same timestamp
        let at_stop_times: Vec<i64> = timepoints
            .iter()
            .filter(|tp| tp.status == LocationStatus::AtStop)
            .map(|tp| tp.timestamp)
            .collect();
        let after_stop_times: Vec<i64> = timepoints
            .iter()
            .filter(|tp| tp.status == LocationStatus::AfterStop)
            .map(|tp| tp.timestamp)
            .collect();

        // Should have same number of AtStop and AfterStop timepoints
        assert_eq!(at_stop_times.len(), after_stop_times.len());
        
        // For zero dwell, AtStop and AfterStop timestamps should be equal for each call
        for (at_time, after_time) in at_stop_times.iter().zip(after_stop_times.iter()) {
            assert_eq!(*at_time, *after_time, "Zero dwell time means AtStop and AfterStop have same timestamp");
        }
    }

    #[test]
    fn test_current_call_status() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Before first stop
        let result = timeline.find_current(900);
        assert_eq!(result.call_index, 0);
        assert_eq!(result.status, LocationStatus::BeforeStop);

        // At first stop arrival
        let result = timeline.find_current(1000);
        assert_eq!(result.call_index, 0);
        assert_eq!(result.status, LocationStatus::AtStop);

        // After first stop departure
        let result = timeline.find_current(1150);
        assert_eq!(result.call_index, 1);
        assert_eq!(result.status, LocationStatus::BetweenStops);

        // At second stop
        let result = timeline.find_current(1200);
        assert_eq!(result.call_index, 1);
        assert_eq!(result.status, LocationStatus::AtStop);

        // After all stops
        let result = timeline.find_current(1600);
        assert_eq!(result.call_index, 2);
        assert_eq!(result.status, LocationStatus::AfterStop);
    }

    #[test]
    fn test_next_state_navigation() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Get first timepoint
        let first_timepoint = &timeline.timepoints[0];
        
        // Test normal progression
        let next = timeline.find_next_state(first_timepoint.timestamp - 10);
        assert_eq!(next, Some(first_timepoint.clone()));

        // Test progression through timepoints
        let second_timepoint = &timeline.timepoints[1];
        let next = timeline.find_next_state(first_timepoint.timestamp);
        assert_eq!(next, Some(second_timepoint.clone()));

        // Test at last timepoint - should return None
        let last_timepoint = timeline.timepoints.last().unwrap();
        let next = timeline.find_next_state(last_timepoint.timestamp);
        assert_eq!(next, None);

        // Test beyond last timepoint - should return None
        let next = timeline.find_next_state(last_timepoint.timestamp + 100);
        assert_eq!(next, None);
    }

    #[test]
    fn test_previous_state_navigation() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Test at first timepoint - should return None
        let first_timepoint = &timeline.timepoints[0];
        let prev = timeline.find_previous_state(first_timepoint.timestamp);
        assert_eq!(prev, None);

        // Test before first timepoint - should return None
        let prev = timeline.find_previous_state(first_timepoint.timestamp - 100);
        assert_eq!(prev, None);

        // Test normal backward progression
        let second_timepoint = &timeline.timepoints[1];
        let prev = timeline.find_previous_state(second_timepoint.timestamp);
        assert_eq!(prev, Some(first_timepoint.clone()));

        // Test from middle of journey
        if timeline.timepoints.len() >= 3 {
            let third_timepoint = &timeline.timepoints[2];
            let prev = timeline.find_previous_state(third_timepoint.timestamp);
            assert_eq!(prev, Some(second_timepoint.clone()));
        }
    }

    #[test]
    fn test_next_stop_exact_status_match() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Start at first AtStop
        let first_at_stop = timeline.timepoints
            .iter()
            .find(|tp| tp.status == LocationStatus::AtStop && tp.call_index == 0)
            .unwrap();

        // Should find next AtStop
        let next_stop = timeline.find_next_stop(first_at_stop.timestamp).unwrap();
        assert_eq!(next_stop.status, LocationStatus::AtStop);
        assert!(next_stop.timestamp > first_at_stop.timestamp);
    }

    #[test]
    fn test_previous_stop_exact_status_match() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Find second AtStop
        let second_at_stop = timeline.timepoints
            .iter()
            .find(|tp| tp.status == LocationStatus::AtStop && tp.call_index == 1)
            .unwrap();

        // Should find previous AtStop
        let prev_stop = timeline.find_previous_stop(second_at_stop.timestamp).unwrap();
        assert_eq!(prev_stop.status, LocationStatus::AtStop);
        assert!(prev_stop.timestamp < second_at_stop.timestamp);
    }

    #[test]
    fn test_next_stop_no_exact_match_fallback() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Find last AtStop
        let last_at_stop = timeline.timepoints
            .iter()
            .rev()
            .find(|tp| tp.status == LocationStatus::AtStop)
            .unwrap();

        // From last AtStop, next_stop should find closest timepoint ahead
        let next_stop = timeline.find_next_stop(last_at_stop.timestamp);
        assert!(next_stop.is_some());
        assert!(next_stop.unwrap().timestamp > last_at_stop.timestamp);
    }

    #[test]
    fn test_previous_stop_no_exact_match_fallback() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Find first AtStop
        let first_at_stop = timeline.timepoints
            .iter()
            .find(|tp| tp.status == LocationStatus::AtStop)
            .unwrap();

        // From first AtStop, previous_stop should find closest timepoint behind
        let prev_stop = timeline.find_previous_stop(first_at_stop.timestamp);
        assert!(prev_stop.is_some());
        assert!(prev_stop.unwrap().timestamp < first_at_stop.timestamp);
    }

    #[test]
    fn test_next_stop_boundary_conditions() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Test at very end - should return last timepoint
        let very_end = timeline.timepoints.last().unwrap().timestamp + 100;
        let next_stop = timeline.find_next_stop(very_end);
        assert_eq!(next_stop, timeline.timepoints.last().cloned());

        // Test with empty timeline
        let empty_timeline = JourneyTimeline { timepoints: vec![] };
        let next_stop = empty_timeline.find_next_stop(1000);
        assert_eq!(next_stop, None);
    }

    #[test]
    fn test_previous_stop_boundary_conditions() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Test at very beginning - should return first timepoint
        let very_beginning = timeline.timepoints.first().unwrap().timestamp - 100;
        let prev_stop = timeline.find_previous_stop(very_beginning);
        assert_eq!(prev_stop, timeline.timepoints.first().cloned());

        // Test with empty timeline
        let empty_timeline = JourneyTimeline { timepoints: vec![] };
        let prev_stop = empty_timeline.find_previous_stop(1000);
        assert_eq!(prev_stop, None);
    }

    #[test]
    fn test_edge_case_insufficient_timepoints() {
        // Create timeline with minimal valid journey
        let minimal_journey = Journey {
            trip_id: "minimal".to_string(),
            destination_text: "Minimal".to_string(),
            line_name: "Minimal Line".to_string(),
            calls: vec![
                create_test_call("stop1", 1, 1000, 1100, None, None),
                create_test_call("stop2", 2, 1200, 1300, None, None),
            ],
        };
        let timeline = JourneyTimeline::from_journey(&minimal_journey).unwrap();

        // Test navigation functions work with minimal timepoints
        assert!(timeline.find_next_state(900).is_some());
        assert!(timeline.find_previous_state(1400).is_some());
        assert!(timeline.find_next_stop(1000).is_some());
        assert!(timeline.find_previous_stop(1300).is_some());
    }

    #[test]
    fn test_timepoint_call_index_consistency() {
        let journey = create_test_journey();
        let timepoints = generate_timepoints(&journey);

        // Verify call_index is within valid range
        for timepoint in &timepoints {
            assert!(
                timepoint.call_index < journey.calls.len(),
                "Call index should be within journey calls range"
            );
        }

        // Verify BetweenStops call_index points to destination call
        for timepoint in &timepoints {
            if timepoint.status == LocationStatus::BetweenStops {
                // call_index should point to the call we're traveling TO
                assert!(timepoint.call_index > 0, "BetweenStops should have call_index > 0");
            }
        }
    }

    #[test]
    fn test_timeline_with_actual_vs_scheduled_times() {
        let journey_with_delays = Journey {
            trip_id: "delayed-trip".to_string(),
            destination_text: "Delayed Destination".to_string(),
            line_name: "Delayed Line".to_string(),
            calls: vec![
                create_test_call("stop1", 1, 1000, 1100, Some(1020), Some(1120)), // 20s delay
                create_test_call("stop2", 2, 1200, 1300, Some(1230), Some(1330)), // 30s delay
                create_test_call("stop3", 3, 1400, 1500, None, None), // No actual times
            ],
        };

        let timeline = JourneyTimeline::from_journey(&journey_with_delays).unwrap();

        // Verify that actual times are used when available
        let first_at_stop = timeline.timepoints
            .iter()
            .find(|tp| tp.status == LocationStatus::AtStop && tp.call_index == 0)
            .unwrap();
        assert_eq!(first_at_stop.timestamp, 1020); // Should use actual arrival time

        let first_after_stop = timeline.timepoints
            .iter()
            .find(|tp| tp.status == LocationStatus::AfterStop && tp.call_index == 0)
            .unwrap();
        assert_eq!(first_after_stop.timestamp, 1120); // Should use actual departure time

        // Verify scheduled times are used as fallback
        let third_at_stop = timeline.timepoints
            .iter()
            .find(|tp| tp.status == LocationStatus::AtStop && tp.call_index == 2)
            .unwrap();
        assert_eq!(third_at_stop.timestamp, 1400); // Should use scheduled time
    }

    #[test]
    fn test_next_state_from_timepoint() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Test normal progression
        let first_timepoint = &timeline.timepoints[0];
        let next = timeline.next_state_from_timepoint(first_timepoint);
        assert!(next.is_some());
        assert_eq!(next.unwrap(), timeline.timepoints[1]);

        // Test from middle timepoint
        if timeline.timepoints.len() >= 3 {
            let middle_timepoint = &timeline.timepoints[1];
            let next = timeline.next_state_from_timepoint(middle_timepoint);
            assert!(next.is_some());
            assert_eq!(next.unwrap(), timeline.timepoints[2]);
        }

        // Test from last timepoint - should return None
        let last_timepoint = timeline.timepoints.last().unwrap();
        let next = timeline.next_state_from_timepoint(last_timepoint);
        assert_eq!(next, None);

        // Test with non-existent timepoint
        let fake_timepoint = TimePoint {
            timestamp: 999999,
            status: LocationStatus::AtStop,
            call_index: 999,
        };
        let next = timeline.next_state_from_timepoint(&fake_timepoint);
        assert_eq!(next, None);
    }

    #[test]
    fn test_previous_state_from_timepoint() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Test from first timepoint - should return None
        let first_timepoint = &timeline.timepoints[0];
        let prev = timeline.previous_state_from_timepoint(first_timepoint);
        assert_eq!(prev, None);

        // Test normal backward progression
        let second_timepoint = &timeline.timepoints[1];
        let prev = timeline.previous_state_from_timepoint(second_timepoint);
        assert!(prev.is_some());
        assert_eq!(prev.unwrap(), timeline.timepoints[0]);

        // Test from middle timepoint
        if timeline.timepoints.len() >= 3 {
            let third_timepoint = &timeline.timepoints[2];
            let prev = timeline.previous_state_from_timepoint(third_timepoint);
            assert!(prev.is_some());
            assert_eq!(prev.unwrap(), timeline.timepoints[1]);
        }

        // Test with non-existent timepoint
        let fake_timepoint = TimePoint {
            timestamp: 999999,
            status: LocationStatus::AtStop,
            call_index: 999,
        };
        let prev = timeline.previous_state_from_timepoint(&fake_timepoint);
        assert_eq!(prev, None);
    }

    #[test]
    fn test_state_from_timepoint_with_identical_timestamps() {
        // Create journey with identical timestamps to test stuck state resolution
        let journey_with_identical_times = Journey {
            trip_id: "identical-times".to_string(),
            destination_text: "Same Time Destination".to_string(),
            line_name: "Same Time Line".to_string(),
            calls: vec![
                create_test_call("stop1", 1, 1000, 1000, Some(1000), Some(1000)), // Same arrival/departure
                create_test_call("stop2", 2, 1100, 1100, Some(1100), Some(1100)), // Same arrival/departure
                create_test_call("stop3", 3, 1200, 1200, Some(1200), Some(1200)), // Same arrival/departure
            ],
        };

        let timeline = JourneyTimeline::from_journey(&journey_with_identical_times).unwrap();

        // Find timepoints with same timestamp but different status
        let at_stop_timepoints: Vec<&TimePoint> = timeline.timepoints
            .iter()
            .filter(|tp| tp.status == LocationStatus::AtStop)
            .collect();

        let after_stop_timepoints: Vec<&TimePoint> = timeline.timepoints
            .iter()
            .filter(|tp| tp.status == LocationStatus::AfterStop)
            .collect();

        // For each AtStop timepoint, next_state_from_timepoint should find the corresponding AfterStop
        for at_stop in &at_stop_timepoints {
            let next = timeline.next_state_from_timepoint(at_stop);
            if let Some(next_point) = next {
                // Should progress to different state even with same timestamp
                assert!(
                    next_point.status != at_stop.status 
                    || next_point.call_index != at_stop.call_index
                    || next_point.timestamp != at_stop.timestamp,
                    "Should progress to different state even with identical timestamp"
                );
            }
        }

        // For each AfterStop timepoint, previous_state_from_timepoint should find the corresponding AtStop
        for after_stop in &after_stop_timepoints {
            let prev = timeline.previous_state_from_timepoint(after_stop);
            if let Some(prev_point) = prev {
                // Should go back to different state even with same timestamp
                assert!(
                    prev_point.status != after_stop.status 
                    || prev_point.call_index != after_stop.call_index
                    || prev_point.timestamp != after_stop.timestamp,
                    "Should go back to different state even with identical timestamp"
                );
            }
        }
    }

    #[test]
    fn test_state_from_timepoint_precision() {
        let journey = create_test_journey();
        let timeline = JourneyTimeline::from_journey(&journey).unwrap();

        // Find all timepoints at the same timestamp but different status/call_index
        let mut grouped_by_timestamp = std::collections::HashMap::new();
        for tp in &timeline.timepoints {
            grouped_by_timestamp.entry(tp.timestamp).or_insert_with(Vec::new).push(tp);
        }

        for (_timestamp, timepoints) in grouped_by_timestamp {
            if timepoints.len() > 1 {
                // Test that each specific timepoint navigates to its exact neighbors
                for (_i, current_tp) in timepoints.iter().enumerate() {
                    // Test next state navigation
                    let next = timeline.next_state_from_timepoint(current_tp);
                    if let Some(next_tp) = next {
                        // Find the position of current timepoint in the full timeline
                        let current_index = timeline.timepoints.iter().position(|tp| {
                            tp.timestamp == current_tp.timestamp 
                                && tp.status == current_tp.status 
                                && tp.call_index == current_tp.call_index
                        }).unwrap();
                        
                        // Next timepoint should be exactly the next one in timeline
                        if current_index + 1 < timeline.timepoints.len() {
                            assert_eq!(next_tp, timeline.timepoints[current_index + 1]);
                        }
                    }

                    // Test previous state navigation  
                    let prev = timeline.previous_state_from_timepoint(current_tp);
                    if let Some(prev_tp) = prev {
                        // Find the position of current timepoint in the full timeline
                        let current_index = timeline.timepoints.iter().position(|tp| {
                            tp.timestamp == current_tp.timestamp 
                                && tp.status == current_tp.status 
                                && tp.call_index == current_tp.call_index
                        }).unwrap();
                        
                        // Previous timepoint should be exactly the previous one in timeline
                        if current_index > 0 {
                            assert_eq!(prev_tp, timeline.timepoints[current_index - 1]);
                        }
                    }
                }
            }
        }
    }
}
