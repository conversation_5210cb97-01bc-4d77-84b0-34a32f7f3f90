use std::str::FromStr;

use anyhow::{Ok, Result};
use baselink_api::{GetActiveTripsRequest, GetTripRequest, GetVehicleTripRequest, Trip, Vehicle, Version};
pub use baselink_api::{
    baselink_client::BaselinkClient, BucketInterceptor,
};
use jiff::Zoned;
use tonic::{
    service::interceptor::InterceptedService,
    transport::{Channel, Endpoint}, Request,
};

#[derive(Clone)]
pub struct BaselinkApi {
    channel: Channel,
    bucket_id: String,
}

impl BaselinkApi {
    pub async fn new_connected(address: &str, bucket_id: String) -> Result<Self> {
        Ok(Self {
            channel: Endpoint::from_str(address)?.connect().await?,
            bucket_id,
        })
    }

    pub fn client(&self) -> BaselinkClient<InterceptedService<Channel, BucketInterceptor>> {
        BaselinkClient::with_interceptor(
            self.channel.clone(),
            BucketInterceptor::new(self.bucket_id.clone()),
        )
    }

    pub async fn get_startup_time(&self) -> Result<i64> {
        let result = self
            .client()
            .get_startup_time(Request::new(()))
            .await?
            .into_inner();
        Ok(result.seconds)
    }

    pub async fn get_version(&self) -> Result<Version> {
        Ok(self.client().get_version(()).await?.into_inner())
    }
    
    pub async fn get_vehicles(&self) -> Result<Vec<Vehicle>> {
        Ok(self.client().get_vehicles(()).await?.into_inner().vehicles)
    }

    pub async fn get_vehicle_trip(&self, vehicle_id: &str) -> Result<Option<Trip>> {
        let request = GetVehicleTripRequest {
            vehicle_id: vehicle_id.to_string(),
        };
        
        let response = self.client().get_vehicle_trip(request).await?.into_inner();
        Ok(response.current_trip)
    }

    pub async fn get_trip(&self, trip_id: &str) -> Result<Option<Trip>> {
        let request = GetTripRequest {
            trip_id: trip_id.to_string(),
            operating_day: Zoned::now().start_of_day()?.timestamp().as_second()
            ,
        };
        
        let response = self.client().get_trip(request).await?.into_inner();
        Ok(Some(response))
    }

    pub async fn get_active_trips(&self, line_name: Option<&str>) -> Result<Vec<Trip>> {
        let now = jiff::Timestamp::now().as_second();
        let request = GetActiveTripsRequest {
            active_at_time: now,
            active_at_range_end: None,
            max_results: 100, // Reasonable default
            agency_id_filter: None,
            line_name_filter: line_name.map(|name| baselink_api::Filter {
                values: vec![name.to_string()],
                mode: baselink_api::filter::FilterMode::IncludeEqual as i32,
            }),
            destination_text_filter: None,
            line_id_filter: None,
            result_start_index: 0,
        };
        
        Ok(self.client().get_active_trips(request).await?.into_inner().trips)
    }


}
