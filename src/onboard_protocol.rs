// defines the protocol for the onboard data


// {
//     "type": "device-identification",
//     "data": {
//         "device_id": "1234567890",
//         "device_classifier": "device-classifier",
//     }
// }

// {
//     "type": "journey",
//     "data": { // reuse onboard.rs::Journey
//         "trip_id": "1234567890",
//         "destination_text": "destination-text",
//         "line_name": "line-name",
//         "calls": [
//             {
//                 "stop_id": "1234567890",
//                 "seq_number": 1,
//                 "scheduled_arrival_time": 1234567890,
//                 "scheduled_departure_time": 1234567890,
//                 "actual_arrival_time": 1234567890,
//                 "actual_departure_time": 1234567890,
//                 "destination_text": "destination-text",
//                 "platform_name": "platform-name",
//                 "stop_name": "stop-name",
//             }  
//         ],
//         "current_call_index": 1,
//         "status": "before-stop",
//     }
// }

// {
//     "type": "vehicle-information",
//     "data": { // reuse onboard.rs::VehicleInformation
//         "vehicle_id": "1234567890",
//         "coach_number": "coach-number",
//         "door_status": "open",
//     }
// }

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use crate::onboard_model::{OnboardData, OnboardDeviceIdentification};

// Server to Client Messages
#[derive(Serialize, Debug, Clone)]
#[serde(tag = "type")]
pub enum ProtocolMessage {
    #[serde(rename = "onboard-data")]
    OnboardData { onboard: OnboardData },
    
    #[serde(rename = "device-identification")]
    DeviceIdentification { 
        #[serde(rename = "deviceId")]
        device_id: String,
        #[serde(rename = "deviceClassifier")]
        device_classifier: String,
    },
    
    #[serde(rename = "error")]
    Error { 
        code: String, 
        message: String 
    },
}

// Client to Server Messages  
#[derive(Deserialize, Debug)]
#[serde(tag = "type")]
pub enum ClientMessage {
    #[serde(rename = "hello")]
    Hello {
        #[serde(rename = "displayId")]
        display_id: String,
        #[serde(flatten)]
        metadata: HashMap<String, serde_json::Value>,
    },
    
    #[serde(rename = "bye")]
    Bye { 
        reason: Option<String> 
    },
}

// Error codes defined by specification
pub mod error_codes {
    pub const DEVICE_NOT_IDENTIFIED: &str = "DEVICE_NOT_IDENTIFIED";
    pub const IDENTIFICATION_TIMEOUT: &str = "IDENTIFICATION_TIMEOUT";
    pub const CONFIG_ERROR: &str = "CONFIG_ERROR";
    pub const HANDSHAKE_FAILED: &str = "HANDSHAKE_FAILED";
    pub const CONNECTION_LOST: &str = "CONNECTION_LOST";
    pub const SEND_FAILED: &str = "SEND_FAILED";
}

// Legacy message type for backwards compatibility during migration
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(tag = "type", rename = "data")]
pub enum OnboardProtocolMessage {
    #[serde(rename = "device-identification")]
    DeviceIdentification (
        OnboardDeviceIdentification,
    ),
    #[serde(rename = "onboard-data")]
    OnboardData (
        OnboardData,
    ),
}
