use std::net::IpAddr;
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use tracing::{info, warn, debug};

// Using OnboardDeviceIdentification from onboard_model.rs
use crate::onboard_model::OnboardDeviceIdentification;

#[derive(Debug, Clone, PartialEq)]
pub struct ConnectionMetadata {
    pub ip: IpAddr,
    pub display_id: Option<String>,
    // DNS hostname resolution can be added later
    pub dns: Option<String>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct IdentifiedDevice {
    pub device_info: OnboardDeviceIdentification,
    pub connection_metadata: ConnectionMetadata,
}

#[derive(Deserialize, Debug)]
struct DeviceConfig {
    #[serde(default)]
    devices: Vec<DeviceEntry>,
}

#[derive(Deserialize, Debug)]
struct DeviceEntry {
    lookup: DeviceLookup,
    #[serde(rename = "device_id")]
    device_id: String,
    #[serde(rename = "device_classifier")]
    device_classifier: String,
}

#[derive(Deserialize, Debug)]
struct DeviceLookup {
    ip: Option<IpAddr>,
    dns: Option<String>,
    #[serde(rename = "display_id")]
    display_id: Option<String>,
}

#[derive(Debug)]
pub struct DeviceIdentificationService {
    config: DeviceConfig,
}

impl DeviceIdentificationService {
    pub fn from_config_file(path: &str) -> Result<Self> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| anyhow!("Failed to read device config file {}: {}", path, e))?;
        
        let config: DeviceConfig = serde_json::from_str(&content)
            .map_err(|e| anyhow!("Failed to parse device config JSON: {}", e))?;
        
        info!("Loaded {} device configurations", config.devices.len());
        Ok(Self { config })
    }

    /// Identify device using AND logic - all specified fields must match
    pub fn identify_device(&self, metadata: &ConnectionMetadata) -> Result<IdentifiedDevice> {
        debug!("Attempting to identify device with metadata: {:?}", metadata);
        
        for (index, entry) in self.config.devices.iter().enumerate() {
            let mut matches = true;
            let mut match_criteria = Vec::new();
            
            // Check IP if specified (primary identification)
            if let Some(expected_ip) = entry.lookup.ip {
                match_criteria.push(format!("ip={}", expected_ip));
                if expected_ip != metadata.ip {
                    matches = false;
                    debug!("Device entry {} IP mismatch: expected {}, got {}", index, expected_ip, metadata.ip);
                }
            }
            
            // Check displayId if specified (from Hello message)
            if let Some(expected_display_id) = &entry.lookup.display_id {
                match_criteria.push(format!("displayId={}", expected_display_id));
                match &metadata.display_id {
                    Some(actual_display_id) if actual_display_id == expected_display_id => {
                        debug!("Device entry {} displayId match: {}", index, actual_display_id);
                    },
                    Some(actual_display_id) => {
                        matches = false;
                        debug!("Device entry {} displayId mismatch: expected {}, got {}", 
                               index, expected_display_id, actual_display_id);
                    },
                    None => {
                        matches = false;
                        debug!("Device entry {} displayId required but not provided", index);
                    }
                }
            }
            
            // Check DNS if specified (future enhancement)
            if let Some(expected_dns) = &entry.lookup.dns {
                match_criteria.push(format!("dns={}", expected_dns));
                // DNS resolution not implemented yet - will always fail if specified
                if metadata.dns.as_ref() != Some(expected_dns) {
                    matches = false;
                    debug!("Device entry {} DNS not implemented/mismatch", index);
                }
            }
            
            if matches {
                info!("Device identified: {} (criteria: {})", entry.device_id, match_criteria.join(", "));
                return Ok(IdentifiedDevice {
                    device_info: OnboardDeviceIdentification {
                        device_id: entry.device_id.clone(),
                        device_classifier: entry.device_classifier.clone(),
                    },
                    connection_metadata: metadata.clone(),
                });
            }
        }
        
        warn!("No matching device configuration found for {:?}", metadata);
        Err(anyhow!("No matching device configuration found"))
    }

    pub fn device_count(&self) -> usize {
        self.config.devices.len()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::IpAddr;
    use std::str::FromStr;

    fn create_test_config() -> DeviceConfig {
        DeviceConfig {
            devices: vec![
                DeviceEntry {
                    lookup: DeviceLookup {
                        ip: Some(IpAddr::from_str("*************").unwrap()),
                        dns: None,
                        display_id: None,
                    },
                    device_id: "door-left-display".to_string(),
                    device_classifier: "onboard-display-information".to_string(),
                },
                DeviceEntry {
                    lookup: DeviceLookup {
                        ip: Some(IpAddr::from_str("*************").unwrap()),
                        dns: None,
                        display_id: Some("display_001".to_string()),
                    },
                    device_id: "door-right-display".to_string(),
                    device_classifier: "onboard-display-information".to_string(),
                },
            ],
        }
    }

    #[test]
    fn test_identify_device_by_ip_only() {
        let service = DeviceIdentificationService {
            config: create_test_config(),
        };

        let metadata = ConnectionMetadata {
            ip: IpAddr::from_str("*************").unwrap(),
            display_id: None,
            dns: None,
        };

        let result = service.identify_device(&metadata).unwrap();
        assert_eq!(result.device_info.device_id, "door-left-display");
        assert_eq!(result.device_info.device_classifier, "onboard-display-information");
    }

    #[test]
    fn test_identify_device_by_ip_and_display_id() {
        let service = DeviceIdentificationService {
            config: create_test_config(),
        };

        let metadata = ConnectionMetadata {
            ip: IpAddr::from_str("*************").unwrap(),
            display_id: Some("display_001".to_string()),
            dns: None,
        };

        let result = service.identify_device(&metadata).unwrap();
        assert_eq!(result.device_info.device_id, "door-right-display");
        assert_eq!(result.device_info.device_classifier, "onboard-display-information");
    }

    #[test]
    fn test_identify_device_missing_display_id() {
        let service = DeviceIdentificationService {
            config: create_test_config(),
        };

        let metadata = ConnectionMetadata {
            ip: IpAddr::from_str("*************").unwrap(),
            display_id: None, // Missing required displayId
            dns: None,
        };

        let result = service.identify_device(&metadata);
        assert!(result.is_err());
    }

    #[test]
    fn test_identify_device_unknown_ip() {
        let service = DeviceIdentificationService {
            config: create_test_config(),
        };

        let metadata = ConnectionMetadata {
            ip: IpAddr::from_str("*************").unwrap(),
            display_id: None,
            dns: None,
        };

        let result = service.identify_device(&metadata);
        assert!(result.is_err());
    }

    #[test]
    fn test_load_actual_config_file() {
        // Test loading the actual devices.jsonc file
        let result = DeviceIdentificationService::from_config_file("devices.jsonc");
        
        match result {
            Ok(service) => {
                assert!(service.device_count() > 0);
                
                // Test identification with the IP from the config file
                let metadata = ConnectionMetadata {
                    ip: IpAddr::from_str("*************").unwrap(),
                    display_id: Some("1".to_string()),
                    dns: None,
                };
                
                let identification_result = service.identify_device(&metadata);
                assert!(identification_result.is_ok());
                
                let identified = identification_result.unwrap();
                assert_eq!(identified.device_info.device_id, "*************");
                assert_eq!(identified.device_info.device_classifier, "onboard-display-information");
            }
            Err(e) => {
                // This test might fail in CI or if the file doesn't exist
                eprintln!("Config file test skipped (file not found): {}", e);
            }
        }
    }
} 