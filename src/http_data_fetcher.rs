use tokio::sync::mpsc;
use tokio::time::{interval, Duration};
use tracing::{info, debug, warn, error, instrument};
use anyhow::Result;

use crate::onboard_model::{OnboardDataMessage, VehicleInformation, VehicleJourney, DoorStatus};
use crate::simulation_manager::SimulationManager;

/// Configuration for the HTTP data fetcher
pub struct HttpDataFetcherConfig {
    pub simulation_state: SimulationManager,
    pub vehicle_id: String,
    pub fetch_interval_seconds: u64,
}

/// Runs the HTTP data fetcher task that reads from simulation state
/// and feeds data to the data coordinator
#[instrument(skip(config, data_tx))]
pub async fn run_http_data_fetcher(
    config: HttpDataFetcherConfig,
    data_tx: mpsc::Sender<OnboardDataMessage>,
) -> Result<()> {
    info!(
        vehicle_id = %config.vehicle_id,
        interval_seconds = config.fetch_interval_seconds,
        "Starting HTTP data fetcher"
    );

    let mut fetch_interval = interval(Duration::from_secs(config.fetch_interval_seconds));
    let mut last_journey_id: Option<String> = None;
    
    // Send initial vehicle information
    let vehicle_info = VehicleInformation {
        vehicle_id: config.vehicle_id.clone(),
        coach_number: Some("1".to_string()),
        door_status: DoorStatus::Closed,
    };
    
    if let Err(e) = data_tx.send(OnboardDataMessage::VehicleInformation(vehicle_info)).await {
        error!(error = %e, "Failed to send initial vehicle information");
        return Err(e.into());
    }

    loop {
        fetch_interval.tick().await;
        
        // Perform tick for playback simulation
        config.simulation_state.tick();
        
        // Get current journey from simulation
        match config.simulation_state.get_current_journey() {
            Some(vehicle_journey) => {
                let current_trip_id = vehicle_journey.journey.trip_id.clone();

                // Check if journey changed or this is first time
                let journey_changed = last_journey_id.as_ref() != Some(&current_trip_id);
                
                if journey_changed {
                    info!(
                        old_journey = ?last_journey_id,
                        new_journey = %current_trip_id,
                        "Journey changed, sending update"
                    );
                    last_journey_id = Some(current_trip_id.clone());
                }

                // Send the journey data (will be deduplicated by coordinator if unchanged)
                if let Err(e) = data_tx.send(OnboardDataMessage::VehicleJourney(vehicle_journey)).await {
                    error!(error = %e, "Failed to send journey data");
                    break;
                }

                if journey_changed {
                    debug!(trip_id = %current_trip_id, "Sent journey update");
                }
            }
            None => {
                // No journey loaded - send OffJourney if we previously had one
                if last_journey_id.is_some() {
                    debug!("No journey loaded, sending OffJourney");
                    if let Err(e) = data_tx.send(OnboardDataMessage::OffJourney).await {
                        error!(error = %e, "Failed to send OffJourney message");
                        break;
                    }
                    last_journey_id = None;
                }
            }
        }
    }

    warn!("HTTP data fetcher loop ended");
    Ok(())
} 