use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::mpsc::UnboundedSender;
use tracing::{debug, info, warn, error};
use anyhow::{Result, anyhow};

use crate::onboard_protocol::{ProtocolMessage, ClientMessage, error_codes};
use crate::device_identification::{DeviceIdentificationService, ConnectionMetadata, IdentifiedDevice};
use crate::onboard_model::OnboardData;

/// Unique identifier for a WebSocket connection
pub type ConnectionId = u64;

/// The main protocol handler for a single WebSocket connection
/// Implements the Onboard Display Protocol state machine
pub struct OnboardDisplayProtocol {
    connection_id: ConnectionId,
    state: ProtocolState,
    device_info: Option<IdentifiedDevice>,
    message_tx: UnboundedSender<ProtocolMessage>,
}

/// Protocol state machine states
#[derive(Debug)]
enum ProtocolState {
    /// Waiting for optional Hello message with 3-second timeout
    AwaitDeviceHello {
        start_time: Instant,
        timeout_duration: Duration,
        connection_metadata: ConnectionMetadata,
    },
    /// Identifying device using the identification service
    IdentifyingDevice {
        identification_service: Arc<DeviceIdentificationService>,
        hello_data: Option<ClientMessage>,
        connection_metadata: ConnectionMetadata,
    },
    /// Streaming onboard data to identified device
    StreamingData {
        device_info: IdentifiedDevice,
        last_data_sent: Option<Instant>,
    },
    /// Connection terminated
    Terminated {
        reason: String,
    },
}

impl OnboardDisplayProtocol {
    /// Create a new protocol instance for a connection
    pub fn new(
        connection_id: ConnectionId,
        connection_metadata: ConnectionMetadata,
        message_tx: UnboundedSender<ProtocolMessage>,
    ) -> Self {
        let timeout_duration = Duration::from_secs(3); // Exactly 3 seconds as per specification
        
        info!(
            connection_id = connection_id,
            ip = %connection_metadata.ip,
            "Starting new protocol instance with 3-second Hello timeout"
        );

        Self {
            connection_id,
            state: ProtocolState::AwaitDeviceHello {
                start_time: Instant::now(),
                timeout_duration,
                connection_metadata,
            },
            device_info: None,
            message_tx,
        }
    }

    /// Check if the Hello timeout has expired
    pub fn is_hello_timeout_expired(&self) -> bool {
        match &self.state {
            ProtocolState::AwaitDeviceHello { start_time, timeout_duration, .. } => {
                start_time.elapsed() >= *timeout_duration
            }
            _ => false,
        }
    }

    /// Handle Hello timeout - transition to device identification
    pub async fn handle_hello_timeout(&mut self, identification_service: Arc<DeviceIdentificationService>) -> Result<()> {
        match &self.state {
            ProtocolState::AwaitDeviceHello { connection_metadata, .. } => {
                info!(
                    connection_id = self.connection_id,
                    "Hello timeout expired (3s), proceeding with device identification"
                );

                self.state = ProtocolState::IdentifyingDevice {
                    identification_service,
                    hello_data: None,
                    connection_metadata: connection_metadata.clone(),
                };

                self.process_device_identification().await
            }
            _ => {
                warn!(
                    connection_id = self.connection_id,
                    state = ?self.state,
                    "Hello timeout called in invalid state"
                );
                Ok(())
            }
        }
    }

    /// Process incoming Hello message
    pub async fn handle_hello_message(
        &mut self,
        hello_message: ClientMessage,
        identification_service: Arc<DeviceIdentificationService>,
    ) -> Result<()> {
        match &self.state {
            ProtocolState::AwaitDeviceHello { connection_metadata, .. } => {
                info!(
                    connection_id = self.connection_id,
                    "Received Hello message, proceeding immediately with device identification"
                );

                // Extract displayId from Hello message if present
                let mut updated_metadata = connection_metadata.clone();
                if let ClientMessage::Hello { display_id, .. } = &hello_message {
                    updated_metadata.display_id = Some(display_id.clone());
                }

                self.state = ProtocolState::IdentifyingDevice {
                    identification_service,
                    hello_data: Some(hello_message),
                    connection_metadata: updated_metadata,
                };

                self.process_device_identification().await
            }
            _ => {
                warn!(
                    connection_id = self.connection_id,
                    state = ?self.state,
                    "Hello message received in invalid state"
                );
                Ok(())
            }
        }
    }

    /// Process device identification
    async fn process_device_identification(&mut self) -> Result<()> {
        match &self.state {
            ProtocolState::IdentifyingDevice { identification_service, connection_metadata, .. } => {
                debug!(
                    connection_id = self.connection_id,
                    metadata = ?connection_metadata,
                    "Attempting device identification"
                );

                match identification_service.identify_device(connection_metadata) {
                    Ok(identified_device) => {
                        info!(
                            connection_id = self.connection_id,
                            device_id = %identified_device.device_info.device_id,
                            device_classifier = %identified_device.device_info.device_classifier,
                            "Device successfully identified"
                        );

                        // Send device identification message
                        let identification_msg = ProtocolMessage::DeviceIdentification {
                            device_id: identified_device.device_info.device_id.clone(),
                            device_classifier: identified_device.device_info.device_classifier.clone(),
                        };

                        if let Err(e) = self.message_tx.send(identification_msg) {
                            error!(
                                connection_id = self.connection_id,
                                error = %e,
                                "Failed to send device identification message"
                            );
                            return Err(anyhow!("Failed to send device identification: {}", e));
                        }

                        // Transition to streaming state
                        self.device_info = Some(identified_device.clone());
                        self.state = ProtocolState::StreamingData {
                            device_info: identified_device,
                            last_data_sent: None,
                        };

                        Ok(())
                    }
                    Err(e) => {
                        warn!(
                            connection_id = self.connection_id,
                            error = %e,
                            "Device identification failed"
                        );

                        // Send error message
                        let error_msg = ProtocolMessage::Error {
                            code: error_codes::DEVICE_NOT_IDENTIFIED.to_string(),
                            message: "Device could not be identified".to_string(),
                        };

                        if let Err(send_err) = self.message_tx.send(error_msg) {
                            error!(
                                connection_id = self.connection_id,
                                error = %send_err,
                                "Failed to send error message"
                            );
                        }

                        // Terminate connection
                        self.state = ProtocolState::Terminated {
                            reason: "Device not identified".to_string(),
                        };

                        Err(anyhow!("Device identification failed: {}", e))
                    }
                }
            }
            _ => {
                error!(
                    connection_id = self.connection_id,
                    "process_device_identification called in invalid state"
                );
                Ok(())
            }
        }
    }

    /// Handle incoming Bye message
    pub async fn handle_bye_message(&mut self, bye_message: ClientMessage) -> Result<()> {
        if let ClientMessage::Bye { reason } = bye_message {
            let reason_text = reason.unwrap_or_else(|| "Client requested disconnect".to_string());
            info!(
                connection_id = self.connection_id,
                reason = %reason_text,
                "Received Bye message from client"
            );

            self.state = ProtocolState::Terminated {
                reason: reason_text,
            };
        }

        Ok(())
    }

    /// Send onboard data to the client (only in StreamingData state)
    pub async fn send_onboard_data(&mut self, data: OnboardData) -> Result<()> {
        match &mut self.state {
            ProtocolState::StreamingData { last_data_sent, .. } => {
                let data_msg = ProtocolMessage::OnboardData { onboard: data };
                
                if let Err(e) = self.message_tx.send(data_msg) {
                    error!(
                        connection_id = self.connection_id,
                        error = %e,
                        "Failed to send onboard data"
                    );
                    return Err(anyhow!("Failed to send onboard data: {}", e));
                }

                *last_data_sent = Some(Instant::now());
                debug!(connection_id = self.connection_id, "Sent onboard data to client");
                Ok(())
            }
            _ => {
                debug!(
                    connection_id = self.connection_id,
                    "Ignoring onboard data send - not in streaming state"
                );
                Ok(())
            }
        }
    }

    /// Check if the protocol is terminated
    pub fn is_terminated(&self) -> bool {
        matches!(self.state, ProtocolState::Terminated { .. })
    }

    /// Get the current state for debugging
    pub fn current_state(&self) -> &str {
        match &self.state {
            ProtocolState::AwaitDeviceHello { .. } => "AwaitDeviceHello",
            ProtocolState::IdentifyingDevice { .. } => "IdentifyingDevice",
            ProtocolState::StreamingData { .. } => "StreamingData",
            ProtocolState::Terminated { .. } => "Terminated",
        }
    }

    /// Get the identified device info (if available)
    pub fn device_info(&self) -> Option<&IdentifiedDevice> {
        self.device_info.as_ref()
    }

    /// Get connection ID
    pub fn connection_id(&self) -> ConnectionId {
        self.connection_id
    }
}
