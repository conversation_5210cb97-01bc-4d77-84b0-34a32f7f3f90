use serde::{Deserialize, Serialize, Deserializer, Serializer};
use jiff::Timestamp;

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub struct OnboardDeviceIdentification {
    /// The device id
    pub device_id: String,

    /// The device classifier
    pub device_classifier: String,

    // /// The device orientation
    // pub device_orientation: Orientation,

    // /// The mount location
    // /// ...
}


// The vehicle information is a group of data that is known regardless of the journey
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct VehicleInformation {
    pub vehicle_id: String,
    pub coach_number: Option<String>,

    /// Realtime door status information, quite general, at this point
    pub door_status: DoorStatus,
    // advanced exit, door information provides precise information about each door and status

    // Composition to be added later a.k.a. train set
}


#[derive(Debug, Clone)]
pub enum OnboardDataMessage {
    VehicleJourney(VehicleJourney),
    OffJourney,
    VehicleInformation(VehicleInformation),
}


#[derive(Serialize, Deserialize, Debug, <PERSON><PERSON>, PartialEq)]
pub enum ExitSide {
    Left,
    Right,
    Unknown,
}


// the  train is composed of multiple vehicles, which may be composed of multiple coaches
// train -> vehicle -> coach
// on all levels there are two directions normal and reverse
// the train when assigned to a Journey gets another level of direction - forward and reverse
// so the presentation devices mounted in coaches need to be aware of itself moounting orientation and the all the directions along the way

// The deviceOrientation(train, vehicle, coach, device): DeviceOrientation
// - train: boolean - wether a train is moving forward or reverse on a Journey
// - vehicle: boolean - wether a vehicle in a connected train is moving forward or reverse
// - coach: boolean - wether a coach in a vehicle is moving forward or reverse to its normal orientation
// - device: boolean - wether a device in a coach is moving forward or reverse to its normal orientation


/// The orientation in reference to the some containter
/// it may be a compound calculation of the orientation of the container and the orientation of the device
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub enum Orientation {
    Forward,
    Reverse,
    Left,
    Right,
    Unknown,
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
pub enum DoorStatus {
    Open,
    Closed,
    Unknown,
}
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct OnboardData {
    #[serde(serialize_with = "serialize_timestamp", deserialize_with = "deserialize_timestamp")]
    pub current_time: Timestamp,
    #[serde(flatten)]
    pub vehicle_information: VehicleInformation,
    #[serde(flatten)]
    pub vehicle_journey: Option<VehicleJourney>,
}

fn serialize_timestamp<S>(timestamp: &Timestamp, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    let formatted = timestamp.strftime("%Y-%m-%dT%H:%M:%SZ").to_string();
    serializer.serialize_str(&formatted)
}

fn deserialize_timestamp<'de, D>(deserializer: D) -> Result<Timestamp, D::Error>
where
    D: Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;
    s.parse::<Timestamp>()
        .map_err(serde::de::Error::custom)
}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct VehicleJourney {
    pub journey: Journey,
    pub current_call_status: Option<CurrentCallStatus>,
}

impl VehicleJourney {
    pub fn current_status(&self) -> Option<LocationStatus> {
        self.current_call_status.as_ref().map(|status| status.status.clone())
    }
    pub fn current_call_index(&self) -> Option<usize> {
        self.current_call_status.as_ref().map(|status| status.call_index)
    }

    pub fn from_journey(journey: Journey) -> Self {
        Self { journey, current_call_status: None }
    }
}


/// Represents the overall status of a trip.
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "PascalCase")]
pub enum LocationStatus {
    BeforeStop,
    AtStop,
    AfterStop,
    BetweenStops,
}

impl LocationStatus {
    pub fn next(&self) -> Self {
        match self {
            LocationStatus::BeforeStop => LocationStatus::AtStop,
            LocationStatus::AtStop => LocationStatus::AfterStop,
            LocationStatus::AfterStop => LocationStatus::BetweenStops,
            LocationStatus::BetweenStops => LocationStatus::BeforeStop,
        }
    }

    pub fn previous(&self) -> Self {
        match self {
            LocationStatus::BeforeStop => LocationStatus::BetweenStops,
            LocationStatus::AtStop => LocationStatus::BeforeStop,
            LocationStatus::AfterStop => LocationStatus::AtStop,
            LocationStatus::BetweenStops => LocationStatus::AfterStop,
        }
    }

}

#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct CurrentCallStatus {
    pub call_index: usize,
    pub status: LocationStatus,
}

/// Represents a single trip with its associated halts.
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Journey {
    pub trip_id: String,
    pub destination_text: String,
    pub line_name: String,
    pub calls: Vec<Call>,
}

/// Represents a single halt (stop) within a trip.
#[derive(Serialize, Deserialize, Debug, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Call {
    pub stop_id: String,
    pub seq_number: u32,

    // Time in seconds since epoch
    pub scheduled_arrival_time: Option<i64>,
    // Time in seconds since epoch
    pub scheduled_departure_time: Option<i64>,
    // Time in seconds since epoch
    pub actual_arrival_time: Option<i64>,
    // Time in seconds since epoch
    pub actual_departure_time: Option<i64>,

    pub destination_text: Option<String>,
    pub platform_name: Option<String>,

    pub stop_name: String,

    pub advisory_messages: Vec<String>,

    pub is_cancelled: bool,

    // Simple exit, door, information
    /// Exit side in the direction of the moving direction (not the normal production direction of the vehicle)
    pub exit_side: ExitSide,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_onboard_data_timestamp_serialization() {
        let timestamp = "2024-01-15T10:30:00Z".parse::<Timestamp>().unwrap();
        
        let onboard_data = OnboardData {
            current_time: timestamp,
            vehicle_information: VehicleInformation {
                vehicle_id: "test-vehicle".to_string(),
                coach_number: Some("1".to_string()),
                door_status: DoorStatus::Closed,
            },
            vehicle_journey: None,
        };

        // Test serialization
        let json = serde_json::to_string(&onboard_data).unwrap();
        println!("Serialized JSON: {}", json);
        
        // Verify the timestamp format in JSON
        assert!(json.contains("\"currentTime\":\"2024-01-15T10:30:00Z\""));
        
        // Verify camelCase field names
        assert!(json.contains("\"vehicleId\":\"test-vehicle\""));
        assert!(json.contains("\"coachNumber\":\"1\""));
        assert!(json.contains("\"doorStatus\":\"Closed\""));

        // Test deserialization
        let deserialized: OnboardData = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.current_time, timestamp);
        assert_eq!(deserialized.vehicle_information.vehicle_id, "test-vehicle");
    }

    #[test]
    fn test_timestamp_deserialization_from_string() {
        let json = r#"{
            "currentTime": "2024-01-15T10:30:00Z",
            "vehicleId": "test-vehicle",
            "coachNumber": "1",
            "doorStatus": "Closed"
        }"#;

        let deserialized: OnboardData = serde_json::from_str(json).unwrap();
        let expected_timestamp = "2024-01-15T10:30:00Z".parse::<Timestamp>().unwrap();
        
        assert_eq!(deserialized.current_time, expected_timestamp);
        assert_eq!(deserialized.vehicle_information.vehicle_id, "test-vehicle");
    }

    #[test]
    fn test_complete_onboard_data_camel_case_serialization() {
        let timestamp = "2024-01-15T10:30:00Z".parse::<Timestamp>().unwrap();
        
        let onboard_data = OnboardData {
            current_time: timestamp,
            vehicle_information: VehicleInformation {
                vehicle_id: "test-vehicle-123".to_string(),
                coach_number: Some("Coach-A".to_string()),
                door_status: DoorStatus::Open,
            },
            vehicle_journey: Some(VehicleJourney {
                journey: Journey {
                    trip_id: "trip-456".to_string(),
                    destination_text: "Central Station".to_string(),
                    line_name: "Line 5".to_string(),
                    calls: vec![
                        Call {
                            stop_id: "stop-001".to_string(),
                            seq_number: 1,
                            scheduled_arrival_time: Some(1700000000),
                            scheduled_departure_time: Some(1700000060),
                            actual_arrival_time: None,
                            actual_departure_time: None,
                            destination_text: Some("Central Station".to_string()),
                            platform_name: Some("Platform 1".to_string()),
                            stop_name: "First Stop".to_string(),
                            advisory_messages: vec!["Delay expected".to_string()],
                            is_cancelled: false,
                            exit_side: ExitSide::Left,
                        }
                    ],
                },
                current_call_status: Some(CurrentCallStatus {
                    call_index: 0,
                    status: LocationStatus::AtStop,
                }),
            }),
        };

        // Test serialization
        let json = serde_json::to_string_pretty(&onboard_data).unwrap();
        println!("Complete OnboardData JSON with camelCase:\n{}", json);
        
        // Verify all camelCase fields are present
        assert!(json.contains("\"currentTime\""));
        assert!(json.contains("\"vehicleId\""));
        assert!(json.contains("\"coachNumber\""));
        assert!(json.contains("\"doorStatus\""));
        assert!(json.contains("\"tripId\""));
        assert!(json.contains("\"destinationText\""));
        assert!(json.contains("\"lineName\""));
        assert!(json.contains("\"stopId\""));
        assert!(json.contains("\"seqNumber\""));
        assert!(json.contains("\"scheduledArrivalTime\""));
        assert!(json.contains("\"scheduledDepartureTime\""));
        assert!(json.contains("\"platformName\""));
        assert!(json.contains("\"stopName\""));
        assert!(json.contains("\"advisoryMessages\""));
        assert!(json.contains("\"isCancelled\""));
        assert!(json.contains("\"exitSide\""));
        assert!(json.contains("\"currentCallStatus\""));
        assert!(json.contains("\"callIndex\""));
    }
}
