use futures_util::{SinkExt, StreamExt};
use std::{
    collections::HashMap,
    net::SocketAddr,
    sync::{Arc, Mutex},
};
use tokio::{
    net::{TcpListener, TcpStream},
    sync::mpsc::{self, UnboundedSender, UnboundedReceiver},
};
use tokio_tungstenite::{accept_async, tungstenite::protocol::Message};
use tracing::{error, info, warn, instrument};
use anyhow::{Context, Result, anyhow};
use crate::onboard_model::OnboardData;
use serde_json;
use crate::onboard_protocol::OnboardProtocolMessage;

#[cfg(test)]
use crate::onboard_model::{VehicleInformation, DoorStatus, Journey, LocationStatus, Call, ExitSide, VehicleJourney};

#[cfg(test)]
use jiff::Timestamp;

// Type alias for the shared state, containing a map of peer addresses to their send channels.
type PeerMap = Arc<Mutex<HashMap<SocketAddr, UnboundedSender<Message>>>>;

// Type alias for the shared state holding the last received OnboardData.
// Wrapped in Arc<Mutex> for thread-safe sharing and modification.
// Option is used because initially, there might be no data received yet.
type LastData = Arc<Mutex<Option<OnboardData>>>;


/// Starts the WebSocket server on the given address and listens for data updates.
///
/// Binds to the address, listens for WebSocket connections, and manages connected clients.
/// Also listens on `data_rx` channel for `OnboardData` updates, stores the latest,
/// and broadcasts them to all connected clients.
#[instrument(skip(data_rx))]
pub async fn start_server(addr: &str, mut data_rx: UnboundedReceiver<OnboardData>) -> Result<()> {
    let listener = TcpListener::bind(addr).await.context("Failed to bind TCP listener")?;
    info!(address = %addr, "WebSocket server listening");

    // Initialize the shared state for peer connections.
    let peer_map_state = PeerMap::new(Mutex::new(HashMap::new()));
    // Initialize the shared state for the last received data. Starts as None.
    let last_data_state = LastData::new(Mutex::new(None::<OnboardData>));

    // Clone Arcs for the broadcast/data handling task.
    let peer_map_for_broadcast = peer_map_state.clone();
    let last_data_for_broadcast = last_data_state.clone();

    // Spawn the task responsible for receiving data updates and broadcasting them.
    tokio::spawn(async move {
        info!("Starting data processing task (receive, store last, broadcast).");
        while let Some(onboard_data) = data_rx.recv().await {
            info!("Received complete OnboardData update via channel.");

            // Update the last known data state - now we receive complete OnboardData
            match last_data_for_broadcast.lock() {
                Ok(mut guard) => {
                    *guard = Some(onboard_data.clone());
                },
                Err(e) => {
                    error!(error = %e, "Failed to lock last_data state for update");
                    // Continue to broadcast even if we can't store it
                }
            }

            // Serialize the received data to JSON for broadcasting.
            match serde_json::to_string(&OnboardProtocolMessage::OnboardData(onboard_data)) {
                Ok(json_string) => {
                    let message = Message::Text(json_string);
                    // Broadcast the new data to all currently connected clients.
                    broadcast_message(&peer_map_for_broadcast, message);
                }
                Err(e) => {
                    // Log if serialization fails. This data won't be broadcasted.
                    error!(error = %e, "Failed to serialize OnboardData to JSON for broadcast");
                }
            }
        }
        // This warning indicates the data source has closed the channel.
        warn!("Data receiver channel closed. Stopping data processing task.");
    });

    // Main server loop to accept incoming connections.
    loop {
        match listener.accept().await {
            Ok((stream, addr)) => {
                // Clone Arcs for the connection handler task.
                let peer_map_clone = peer_map_state.clone();
                let last_data_clone = last_data_state.clone(); // Clone last_data state for the handler
                // Spawn a task for each new connection.
                tokio::spawn(async move {
                    // Define an inner async function for better instrumentation context.
                    #[instrument(skip(peer_map_clone, last_data_clone, stream), fields(peer_addr = %addr))]
                    async fn connection_task(
                        peer_map_clone: PeerMap,
                        last_data_clone: LastData, // Pass last_data state
                        stream: TcpStream,
                        addr: SocketAddr
                    ) {
                        info!("Incoming TCP connection");
                        // Call handle_connection with both state Arcs.
                        if let Err(e) = handle_connection(peer_map_clone, last_data_clone, stream, addr).await {
                            error!(error = %e, "Connection handler failed");
                        }
                    }
                    // Execute the connection task.
                    connection_task(peer_map_clone, last_data_clone, stream, addr).await;
                });
            }
            Err(e) => {
                // Log errors during connection acceptance.
                error!(error = %e, "Failed to accept incoming connection");
                // Consider if the server should stop or continue after accept errors.
            }
        }
    }
}

/// Handles an individual WebSocket connection.
///
/// Performs the handshake, manages the connection lifecycle, sends the last known data
/// upon connection, and handles message exchange for a single client.
#[instrument(skip(peer_map, last_data, raw_stream), fields(peer_addr = %addr))]
async fn handle_connection(
    peer_map: PeerMap,
    last_data: LastData, // Accept the last data state
    raw_stream: TcpStream,
    addr: SocketAddr
) -> Result<()> {
    // Perform the WebSocket handshake.
    let ws_stream = accept_async(raw_stream)
        .await
        .context("WebSocket handshake failed")?;
    info!("WebSocket connection established");

    // Create a channel for sending messages specifically to this client.
    let (tx, mut rx) = mpsc::unbounded_channel::<Message>();

    // Add the client's sender channel to the shared PeerMap.
    peer_map
        .lock()
        .map_err(|e| anyhow!("Mutex poisoned (peer_map insert): {}", e))? // Provide context
        .insert(addr, tx.clone()); // Clone tx for insertion, keep original for sending last data

    // --- Send last known data to the new client ---
    { // Scoped block for the mutex lock guard
        let last_data_guard = last_data
            .lock()
            .map_err(|e| anyhow!("Mutex poisoned (last_data read): {}", e))?;

        if let Some(ref data) = *last_data_guard {
            info!("Found last known data, sending to new client.");
            match serde_json::to_string(data) {
                Ok(json_string) => {
                    let message = Message::Text(json_string);
                    // Use the client's specific tx channel.
                    if let Err(e) = tx.send(message) {
                        // Error sending likely means client disconnected immediately.
                        error!(error = %e, "Failed to send last data to newly connected client");
                        // We might want to return early here, but let's continue connection setup for now.
                        // The broadcast_task might handle the cleanup later if the channel is broken.
                    } else {
                         info!("Successfully sent last known data to new client.");
                    }
                }
                Err(e) => {
                    // Log serialization error, but don't crash the connection handler.
                    error!(error = %e, "Failed to serialize last OnboardData for new client");
                }
            }
        } else {
             info!("No last known data available to send to new client.");
        }
    } // Mutex guard drops here

    // Split the WebSocket stream into sender and receiver halves.
    let (mut outgoing, mut incoming) = ws_stream.split();

    // Task to forward messages from the client's channel (rx) to the WebSocket sink (outgoing).
    // This handles broadcasts and the initial "last data" message.
    let broadcast_task = tokio::spawn(async move {
        while let Some(message) = rx.recv().await {
            if outgoing.send(message).await.is_err() {
                // Log failure, client likely disconnected. Stop trying to send.
                warn!("Failed to send message via WebSocket sink, client likely disconnected.");
                break; // Exit the loop if sending fails.
            }
        }
        info!("Client send task finished (either channel closed or WebSocket send failed).");
    });

    // Task to process messages received from the WebSocket stream (incoming).
    let receive_task = tokio::spawn(async move {
        while let Some(result) = incoming.next().await {
            match result {
                Ok(msg) => {
                    // Log received messages. Handle specific message types like Close.
                    info!(message = ?msg, "Received message from client");
                    if msg.is_close() {
                        info!("Received WebSocket Close frame from client.");
                        break; // Exit loop on receiving Close frame.
                    }
                    // Handle other message types if needed (e.g., Ping/Pong, Binary).
                    // Currently, we just log them.
                }
                Err(e) => {
                    // Log errors receiving messages (e.g., network issues, protocol errors).
                    warn!(error = %e, "Error receiving message from client via WebSocket stream");
                    break; // Exit loop on receive error.
                }
            }
        }
        info!("Client receive task finished (either received Close frame or error).");
    });

    // Wait for either the send task or receive task to complete.
    // This indicates the connection should be terminated (e.g., client disconnected, error occurred).
    tokio::select! {
        _ = broadcast_task => { info!("Send task completed or failed, triggering disconnect."); },
        _ = receive_task => { info!("Receive task completed or failed, triggering disconnect."); },
    }

    // --- Cleanup ---
    info!("Disconnecting client and cleaning up resources.");
    // Remove the client's sender channel from the PeerMap.
    peer_map
        .lock()
        .map_err(|e| anyhow!("Mutex poisoned (peer_map remove): {}", e))? // Provide context
        .remove(&addr);

    info!("Client disconnected and removed from map");
    Ok(())
}

/// Broadcasts a message to all connected clients.
///
/// Iterates through the peer map and sends the given message to each client's channel.
/// Logs warnings if sending to a specific client fails (e.g., if the client disconnected).
#[instrument(skip(peer_map))]
fn broadcast_message(peer_map: &PeerMap, message: Message) {
    let peers = peer_map.lock().expect("Mutex was poisoned");

    for (addr, tx) in peers.iter() {
        match tx.send(message.clone()) {
            Ok(_) => {
                info!(peer_addr = %addr, "Successfully broadcasted message");
            }
            Err(_) => {
                warn!(peer_addr = %addr, "Failed to send message to client, client might have disconnected.");
            }
        }
    }
}

// Module for integration tests
#[cfg(test)]
mod tests {
    use super::*; // Import items from the parent module (service)
    use tokio::time::{sleep, Duration};
    use tokio_tungstenite::{connect_async, MaybeTlsStream, WebSocketStream};
    use url::Url;
    use crate::onboard_model::{Call, CurrentCallStatus, DoorStatus, ExitSide, Journey, LocationStatus, OnboardDeviceIdentification, Orientation};
    use futures_util::stream::SplitStream;

    type ClientReadStream = SplitStream<WebSocketStream<MaybeTlsStream<TcpStream>>>;

    /// Tests the broadcast functionality by simulating multiple clients.
    ///
    /// This test performs the following steps:
    /// 1. Starts a WebSocket server on a random available port.
    /// 2. Spawns multiple client tasks that connect to the server.
    /// 3. Waits for clients to establish connections.
    /// 4. Calls the `broadcast_message` function to send a message to all clients.
    /// 5. Verifies that each client receives the broadcasted message correctly.
    #[tokio::test]
    async fn test_broadcast_to_multiple_clients() {
        // 1. Setup: Start the server in the background
        // Bind to port 0 to let the OS assign a random available port.
        let listener = TcpListener::bind("127.0.0.1:0").await.expect("Failed to bind test listener");
        let addr = listener.local_addr().expect("Failed to get local address");
        let server_url_str = format!("ws://{}", addr);
        let server_url = Url::parse(&server_url_str).expect("Failed to parse server URL");
        info!(url = %server_url, "Test server listening");

        // Create the shared state (PeerMap and LastData) for the test server.
        let peer_map = PeerMap::new(Mutex::new(HashMap::new()));
        // Also create the last data state for the test server environment.
        let last_data = LastData::new(Mutex::new(None::<OnboardData>)); // Initialize empty
        let server_peer_map = peer_map.clone(); // Clone Arc for the server task.
        let server_last_data = last_data.clone(); // Clone Arc for the server task.

        // Spawn the server logic task.
        // This task will accept incoming connections and use `handle_connection`.
        tokio::spawn(async move {
            loop {
                match listener.accept().await {
                    Ok((stream, client_addr)) => {
                        // Clone the PeerMap and LastData Arcs for each connection handler.
                        let state_clone = server_peer_map.clone();
                        let last_data_clone = server_last_data.clone(); // Clone last_data state
                        // Spawn a task to handle the new connection.
                        tokio::spawn(async move {
                            info!(peer_addr = %client_addr, "Test server accepted connection");
                            // Use the actual handle_connection function from the outer scope, passing both states.
                            if let Err(e) = handle_connection(state_clone, last_data_clone, stream, client_addr).await {
                                // Log errors from the connection handler.
                                error!(peer_addr = %client_addr, error = %e, "Test handle_connection failed");
                            }
                        });
                    }
                    Err(e) => {
                        // Log error if the server fails to accept a connection.
                        error!(error = %e, "Test server failed to accept connection");
                        // In a real server, you might want more sophisticated error handling or shutdown logic here.
                        // For this test, we'll just log and potentially break the loop.
                        break; // Exit loop on accept error
                    }
                }
            }
        });

        // Brief pause to ensure the server task starts listening.
        // NOTE: This is a common but potentially flaky way to synchronize.
        // More robust methods include using Barriers or notification channels.
        sleep(Duration::from_millis(100)).await;

        // 2. Simulate Clients Connecting
        const NUM_CLIENTS: usize = 3;
        let mut client_handles = Vec::new();

        // Launch connection tasks for each simulated client.
        for i in 0..NUM_CLIENTS {
            let url_str = server_url_str.clone(); // Clone the string URL for the task
            let handle = tokio::spawn(async move {
                info!(client_id = i, url = %url_str, "Client attempting to connect");
                // Pass the string URL directly to connect_async
                match connect_async(url_str).await { // <-- Use the string here
                    Ok((ws_stream, response)) => {
                        info!(client_id = i, status = ?response.status(), "Client connected successfully");
                        let (_write, read) = ws_stream.split();
                        (Some(read), i)
                    }
                    Err(e) => {
                        // Log connection errors for diagnostics.
                        error!(client_id = i, error = %e, "Client failed to connect");
                        (None, i)
                    }
                }
            });
            client_handles.push(handle);
        }

        // Initialize the vector with capacity and push results
        let mut client_read_streams: Vec<Option<ClientReadStream>> = Vec::with_capacity(NUM_CLIENTS);
        for _ in 0..NUM_CLIENTS { // Prefill with None to allow indexing
            client_read_streams.push(None);
        }

        for handle in client_handles {
            let (stream_option, index) = handle.await.expect("Client connection task panicked");
            if let Some(stream) = stream_option {
                // Assign the stream to the correct index
                client_read_streams[index] = Some(stream);
                info!(client_id = index, "Successfully collected read stream for client");
            } else {
                // If any client failed to connect, fail the test immediately.
                panic!("Client {} failed to connect during setup", index);
            }
        }
        // Double-check that all streams were collected (should be guaranteed by the panic above).
        assert!(client_read_streams.iter().all(|s| s.is_some()), "Not all clients connected successfully");
        info!("All clients connected and read streams collected.");

        // Allow time for server `handle_connection` tasks to run and insert clients into PeerMap.
        // Again, a Barrier would be more reliable synchronization.
        sleep(Duration::from_millis(200)).await;

        // 3. Broadcast a Message
        let broadcast_msg_text = format!("Broadcast test message @ {}", std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_millis());
        let broadcast_msg = Message::Text(broadcast_msg_text.clone());
        info!(message = %broadcast_msg_text, "Broadcasting message to clients");
        // Call the function under test, passing the test's PeerMap instance.
        // Note: This test only verifies broadcast, not the "last data on connect" feature yet.
        broadcast_message(&peer_map, broadcast_msg);

        // 4. Verify Clients Received the Message
        let mut verification_handles = Vec::new();
        // Iterate through the collected client read streams.
        for (i, stream_option) in client_read_streams.into_iter().enumerate() {
            let expected_text = broadcast_msg_text.clone(); // Clone expected text for the task.
            let handle = tokio::spawn(async move {
                // We asserted non-None earlier, so unwrap is safe here.
                let mut stream = stream_option.expect("Stream was None during verification");
                info!(client_id = i, "Client waiting for message...");
                // Wait for the next message, with a timeout to prevent hangs.
                match tokio::time::timeout(Duration::from_secs(2), stream.next()).await {
                    Ok(Some(Ok(msg))) => {
                        // Message received within timeout.
                        info!(client_id = i, message = ?msg, "Client received message");
                        // Assert that the received message matches the expected broadcast message.
                        assert_eq!(msg, Message::Text(expected_text),
                                   "Client {} received incorrect message", i);
                        info!(client_id = i, "Client received the correct message.");
                    }
                    Ok(Some(Err(e))) => {
                        // An error occurred while receiving.
                        panic!("Client {} received error instead of message: {}", i, e);
                    }
                    Ok(None) => {
                        // The stream closed before a message was received.
                        panic!("Client {} stream closed unexpectedly.", i);
                    }
                    Err(_) => {
                        // The timeout elapsed before receiving a message.
                        panic!("Client {} timed out waiting for broadcast message.", i);
                    }
                }
            });
            verification_handles.push(handle);
        }

        // Wait for all verification tasks to complete.
        for (i, handle) in verification_handles.into_iter().enumerate() {
            // `await` the handle. If the task panicked (e.g., due to assertion failure or timeout),
            // this will propagate the panic, failing the test.
            if let Err(e) = handle.await {
                panic!("Verification task for client {} failed: {:?}", i, e);
            }
        }

        info!("All clients successfully verified receipt of the broadcast message.");

        // 5. Cleanup (Optional but Recommended)
        // Explicitly closing connections or signaling the server to shut down
        // can make tests cleaner and prevent resource leaks, especially in larger test suites.
        // For this example, we rely on tasks ending and dropping resources.
        // Consider adding graceful shutdown mechanisms for more complex scenarios.
    }

    /// Tests that a newly connecting client receives the last known data.
    ///
    /// This test performs the following steps:
    /// 1. Creates a sample `OnboardData`.
    /// 2. Starts a WebSocket server with the sample data pre-populated in `LastData`.
    /// 3. Spawns a single client task that connects to the server.
    /// 4. Verifies that the client immediately receives the pre-populated data upon connection.
    #[tokio::test]
    async fn test_new_client_receives_last_data() {
        // 1. Setup: Prepare initial data and start the server
        // Create a sample OnboardData instance.
        // NOTE: Replace this with an actual OnboardData instance if needed for the test.
        // Ensure OnboardData derives Debug, Clone, PartialEq, and serde::Serialize.
        let initial_data = OnboardData {
            current_time: Timestamp::now(),
            vehicle_information: VehicleInformation {
                vehicle_id: "test-vehicle".to_string(),
                coach_number: Some("1".to_string()),
                door_status: DoorStatus::Closed,
            },
            vehicle_journey: Some(VehicleJourney {
                journey: Journey {
                trip_id: "trip-123".to_string(),
                destination_text: "Central Station".to_string(),
                line_name: "Line 5".to_string(),
                calls: vec![
                    Call {
                        stop_id: "stop-A".to_string(),
                        seq_number: 1,
                        scheduled_arrival_time: Some(1700000000),
                        scheduled_departure_time: Some(1700000060),
                        actual_arrival_time: None,
                        actual_departure_time: None,
                        destination_text: Some("Central Station".to_string()),
                        platform_name: Some("Pl. 1".to_string()),
                        stop_name: "Alpha Stop".to_string(),
                        advisory_messages: vec![],
                        is_cancelled: false,
                        exit_side: ExitSide::Unknown,
                    },
                    Call {
                        stop_id: "stop-B".to_string(),
                        seq_number: 2,
                        scheduled_arrival_time: Some(1700000180),
                        scheduled_departure_time: Some(1700000240),
                        actual_arrival_time: None,
                        actual_departure_time: None,
                        destination_text: Some("Central Station".to_string()),
                        platform_name: None,
                        stop_name: "Beta Stop".to_string(),
                        advisory_messages: vec![],
                        is_cancelled: false,
                        exit_side: ExitSide::Unknown,
                    },
                ],
                },
                current_call_status: None,
            }),
        };
        let initial_data_json = serde_json::to_string(&initial_data).expect("Failed to serialize initial data");

        // Bind to port 0 for a random available port.
        let listener = TcpListener::bind("127.0.0.1:0").await.expect("Failed to bind test listener");
        let addr = listener.local_addr().expect("Failed to get local address");
        let server_url_str = format!("ws://{}", addr);
        info!(url = %server_url_str, "Test server listening for last data test");

        // Create shared state, pre-populating LastData.
        let peer_map = PeerMap::new(Mutex::new(HashMap::new()));
        // IMPORTANT: Initialize LastData with the sample data.
        let last_data = LastData::new(Mutex::new(Some(initial_data.clone())));
        let server_peer_map = peer_map.clone();
        let server_last_data = last_data.clone();

        // Spawn the server task (similar to the broadcast test).
        tokio::spawn(async move {
            loop {
                match listener.accept().await {
                    Ok((stream, client_addr)) => {
                        let pm_clone = server_peer_map.clone();
                        let ld_clone = server_last_data.clone();
                        tokio::spawn(async move {
                            info!(peer_addr = %client_addr, "Test server accepted connection (last data test)");
                            if let Err(e) = handle_connection(pm_clone, ld_clone, stream, client_addr).await {
                                error!(peer_addr = %client_addr, error = %e, "Test handle_connection failed (last data test)");
                            }
                        });
                    }
                    Err(e) => {
                        error!(error = %e, "Test server failed to accept connection (last data test)");
                        break;
                    }
                }
            }
        });

        // Brief pause for the server to start.
        sleep(Duration::from_millis(100)).await;

        // 2. Simulate Client Connecting
        info!(url = %server_url_str, "Client attempting to connect (last data test)");
        let connect_future = connect_async(&server_url_str); // Pass URL reference

        match tokio::time::timeout(Duration::from_secs(5), connect_future).await {
            Ok(Ok((ws_stream, response))) => {
                info!(status = ?response.status(), "Client connected successfully (last data test)");
                let (_write, mut read) = ws_stream.split();

                // 3. Verify Receiving the Initial Message
                info!("Client waiting for initial message (last data test)...");
                match tokio::time::timeout(Duration::from_secs(2), read.next()).await {
                    Ok(Some(Ok(msg))) => {
                        info!(message = ?msg, "Client received initial message (last data test)");
                        // Assert that the first message received matches the initial data.
                        assert_eq!(
                            msg,
                            Message::Text(initial_data_json),
                            "Client received incorrect initial data"
                        );
                        info!("Client received the correct initial data.");
                    }
                    Ok(Some(Err(e))) => {
                        panic!("Client received error instead of initial message: {}", e);
                    }
                    Ok(None) => {
                        panic!("Client stream closed before receiving initial message.");
                    }
                    Err(_) => {
                        panic!("Client timed out waiting for initial message.");
                    }
                }
            }
            Ok(Err(e)) => {
                panic!("Client failed to connect (last data test): {}", e);
            }
            Err(_) => {
                panic!("Client timed out connecting to the server (last data test)");
            }
        }

        // 4. Cleanup (Optional) - Server task will stop when listener is dropped.
        info!("Last data test completed successfully.");
    }
} 