use futures_util::{SinkExt, StreamExt};
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::{
    net::{TcpListener, TcpStream},
    sync::mpsc::{self, UnboundedSender, UnboundedReceiver},
};
use tokio_tungstenite::{accept_async, tungstenite::protocol::Message};
use tracing::{debug, error, info, warn, instrument};
use anyhow::{Context, Result, anyhow};

use crate::onboard_model::OnboardData;
use crate::onboard_protocol::{ProtocolMessage, ClientMessage};
use crate::device_identification::DeviceIdentificationService;
use crate::protocol_manager::{ProtocolManager, ConnectionMessage};
use crate::onboard_display_protocol::ConnectionId;

/// Starts the WebSocket server with protocol manager
///
/// This is the new Phase 2 implementation that uses the protocol state machine
/// instead of simple broadcasting. The data_coordinator.rs remains unchanged.
#[instrument(skip(data_rx))]
pub async fn start_server(addr: &str, data_rx: UnboundedReceiver<OnboardData>) -> Result<()> {
    let listener = TcpListener::bind(addr).await.context("Failed to bind TCP listener")?;
    info!(address = %addr, "WebSocket server listening");

    // Load device identification service
    let identification_service = Arc::new(
        DeviceIdentificationService::from_config_file("devices.jsonc")
            .context("Failed to load device identification service")?
    );
    info!("Device identification service loaded with {} devices", identification_service.device_count());

    // Create protocol manager
    let protocol_manager = ProtocolManager::new(identification_service, data_rx);

    // Create channel for connection messages
    let (connection_tx, connection_rx) = mpsc::unbounded_channel::<ConnectionMessage>();

    // Spawn protocol manager task
    tokio::spawn(async move {
        if let Err(e) = protocol_manager.run(connection_rx).await {
            error!(error = %e, "Protocol manager failed");
        }
    });

    // Connection counter for unique IDs
    let mut connection_counter = 0u64;

    // Main server loop to accept incoming connections
    loop {
        match listener.accept().await {
            Ok((stream, addr)) => {
                connection_counter += 1;
                let connection_id = connection_counter;

                info!(
                    connection_id = connection_id,
                    addr = %addr,
                    "Accepting new WebSocket connection"
                );

                let connection_tx_clone = connection_tx.clone();

                // Spawn a task for each new connection
                tokio::spawn(async move {
                    if let Err(e) = handle_connection(connection_id, stream, addr, connection_tx_clone).await {
                        error!(
                            connection_id = connection_id,
                            addr = %addr,
                            error = %e,
                            "Connection handler failed"
                        );
                    }
                });
            }
            Err(e) => {
                error!(error = %e, "Failed to accept incoming connection");
            }
        }
    }
}

/// Handles an individual WebSocket connection using the protocol state machine
#[instrument(skip(raw_stream, connection_tx), fields(connection_id = connection_id, peer_addr = %addr))]
async fn handle_connection(
    connection_id: ConnectionId,
    raw_stream: TcpStream,
    addr: SocketAddr,
    connection_tx: UnboundedSender<ConnectionMessage>,
) -> Result<()> {
    // Perform the WebSocket handshake
    let ws_stream = accept_async(raw_stream)
        .await
        .context("WebSocket handshake failed")?;
    info!("WebSocket connection established");

    // Create a channel for sending protocol messages to this client
    let (protocol_tx, mut protocol_rx) = mpsc::unbounded_channel::<ProtocolMessage>();

    // Notify protocol manager about new connection
    let new_connection_msg = ConnectionMessage::NewConnection {
        connection_id,
        addr,
        message_tx: protocol_tx,
    };

    if let Err(e) = connection_tx.send(new_connection_msg) {
        error!(
            connection_id = connection_id,
            error = %e,
            "Failed to notify protocol manager of new connection"
        );
        return Err(anyhow!("Failed to register connection with protocol manager"));
    }

    // Split the WebSocket stream into sender and receiver halves
    let (mut outgoing, mut incoming) = ws_stream.split();

    // Clone connection_tx for the receive task
    let connection_tx_for_receive = connection_tx.clone();

    // Task to forward protocol messages to the WebSocket
    let send_task = tokio::spawn(async move {
        while let Some(protocol_message) = protocol_rx.recv().await {
            // Serialize protocol message to JSON
            match serde_json::to_string(&protocol_message) {
                Ok(json_string) => {
                    let message = Message::Text(json_string);
                    if outgoing.send(message).await.is_err() {
                        warn!(
                            connection_id = connection_id,
                            "Failed to send message via WebSocket, client likely disconnected"
                        );
                        break;
                    }
                }
                Err(e) => {
                    error!(
                        connection_id = connection_id,
                        error = %e,
                        "Failed to serialize protocol message"
                    );
                }
            }
        }
        info!(connection_id = connection_id, "Send task finished");
    });

    // Task to process messages received from the WebSocket stream
    let receive_task = tokio::spawn(async move {
        while let Some(result) = incoming.next().await {
            match result {
                Ok(msg) => {
                    match msg {
                        Message::Text(text) => {
                            // Try to parse as ClientMessage
                            match serde_json::from_str::<ClientMessage>(&text) {
                                Ok(client_message) => {
                                    info!(
                                        connection_id = connection_id,
                                        message = ?client_message,
                                        "Received client message"
                                    );

                                    // Send to protocol manager
                                    let conn_msg = ConnectionMessage::ClientMessage {
                                        connection_id,
                                        message: client_message,
                                    };

                                    if let Err(e) = connection_tx_for_receive.send(conn_msg) {
                                        error!(
                                            connection_id = connection_id,
                                            error = %e,
                                            "Failed to send client message to protocol manager"
                                        );
                                        break;
                                    }
                                }
                                Err(e) => {
                                    warn!(
                                        connection_id = connection_id,
                                        text = %text,
                                        error = %e,
                                        "Failed to parse client message"
                                    );
                                }
                            }
                        }
                        Message::Close(_) => {
                            info!(connection_id = connection_id, "Received WebSocket Close frame");
                            break;
                        }
                        Message::Ping(data) => {
                            // WebSocket ping/pong is handled automatically by tungstenite
                            debug!(connection_id = connection_id, "Received ping");
                        }
                        Message::Pong(_) => {
                            debug!(connection_id = connection_id, "Received pong");
                        }
                        _ => {
                            debug!(connection_id = connection_id, "Received other message type");
                        }
                    }
                }
                Err(e) => {
                    warn!(
                        connection_id = connection_id,
                        error = %e,
                        "Error receiving message from WebSocket"
                    );
                    break;
                }
            }
        }
        info!(connection_id = connection_id, "Receive task finished");
    });

    // Wait for either the send task or receive task to complete
    tokio::select! {
        _ = send_task => {
            info!(connection_id = connection_id, "Send task completed, triggering disconnect");
        },
        _ = receive_task => {
            info!(connection_id = connection_id, "Receive task completed, triggering disconnect");
        },
    }

    // Cleanup - notify protocol manager that connection is closed
    let close_msg = ConnectionMessage::ConnectionClosed { connection_id };
    if let Err(e) = connection_tx.send(close_msg) {
        warn!(
            connection_id = connection_id,
            error = %e,
            "Failed to notify protocol manager of connection closure"
        );
    }

    info!(connection_id = connection_id, "Connection handler finished");
    Ok(())
}

// Note: The old broadcast_message function has been removed as it's no longer needed.
// The protocol manager now handles all message broadcasting through the state machine.

// Note: The old integration tests have been removed as they were specific to the
// simple broadcast implementation. New tests for the protocol state machine
// should be added using websocat as specified in the implementation plan.

#[cfg(test)]
mod tests {
    // TODO: Add new integration tests for the protocol state machine
    // These should test:
    // 1. 3-second Hello timeout behavior
    // 2. Hello message processing and immediate identification
    // 3. Error messages for unidentified devices
    // 4. Multiple concurrent connections
    // 5. Device identification with IP + displayId
    //
    // Use websocat for testing as specified in the implementation plan
}
