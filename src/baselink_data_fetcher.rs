use anyhow::{Context, Result};
use baselink_api::Trip;
use baselink_api::{baselink_client::BaselinkClient, BucketInterceptor, GetVehicleTripRequest};
use std::time::Duration;
use tokio::sync::mpsc;
use tokio::time::{interval, MissedTickBehavior};
use tonic::service::interceptor::InterceptedService;
use tonic::transport::Channel;
use tracing::{debug, error, info, instrument, warn};

use crate::baselink_api_client::BaselinkApi;
use crate::onboard_model::{Journey, OnboardDataMessage, VehicleJourney}; // Use the existing BaselinkApi struct

// Import the conversion traits - needed for .into() to work
#[allow(unused_imports)]
use crate::from_baselink::*;
use crate::journey_simulation::{JourneySimulation, ModificationError};

/// Configuration for the data fetcher task.
#[derive(Clone)]
pub struct DataFetcherConfig {
    /// An instance of our BaselinkApi wrapper.
    pub baselink_api: BaselinkApi,
    /// The ID of the vehicle to fetch data for.
    pub vehicle_id: String,
    /// Interval in seconds between fetch attempts.
    pub fetch_interval_seconds: u64,
}

/// Runs the periodic data fetching task.
///
/// Fetches vehicle trip data from Baselink at a configured interval,
/// converts it to `OnboardData`, compares it with the previously fetched data,
/// and sends it through the channel if it has changed.
#[instrument(skip(config, data_sender))]
pub async fn run_data_fetcher(config: DataFetcherConfig, data_sender: mpsc::Sender<OnboardDataMessage>) -> Result<()> {
    info!(
        vehicle_id = %config.vehicle_id,
        interval_sec = config.fetch_interval_seconds,
        "Starting data fetcher task"
    );

    let mut interval = interval(Duration::from_secs(config.fetch_interval_seconds));
    interval.set_missed_tick_behavior(MissedTickBehavior::Skip);

    let mut last_vehicle_journey: Option<VehicleJourney> = None;
    let mut journey_simulation: Option<JourneySimulation> = None;

    loop {
        interval.tick().await; // Wait for the next interval tick

        debug!("Fetching data from Baselink...");
        let mut client = config.baselink_api.client();

        let now = jiff::Timestamp::now();

        // Fetch trip data from Baselink
        let journey: Option<Journey> = match fetch_vehicle_trip(&mut client, &config.vehicle_id)
            .await
        {
            Ok(Some(trip)) => Some(trip.into()),
            Ok(None) => {
                warn!("No trip data found for vehicle {}", config.vehicle_id);
                None
            }
            Err(e) => {
                error!("Failed to fetch data from Baselink: {:?}", e);
                // Send OffJourney message when there's an error fetching trip from baselink
                if let Err(send_err) = data_sender.send(OnboardDataMessage::OffJourney).await {
                    warn!(error = %send_err, "Stopping data fetcher task due to closed channel.");
                    break;
                }
                continue;
            }
        };

        // Process the journey data
        let current_vehicle_journey = match process_journey_data(&mut journey_simulation, journey, now.as_second()) {
            Ok(vehicle_journey) => vehicle_journey,
            Err(e) => {
                warn!("Journey processing failed: {}", e);
                // Send OffJourney message when Journey is invalid
                if let Err(send_err) = data_sender.send(OnboardDataMessage::OffJourney).await {
                    warn!(error = %send_err, "Stopping data fetcher task due to closed channel.");
                    break;
                }
                continue;
            }
        };

        // Check if data is different from the last known state
        if last_vehicle_journey.as_ref() != current_vehicle_journey.as_ref() {
            match current_vehicle_journey {
                Some(vehicle_journey) => {
                    info!("Data changed, sending VehicleJourney update.");
                    if let Err(e) = data_sender.send(OnboardDataMessage::VehicleJourney(vehicle_journey.clone())).await {
                        warn!(error = %e, "Stopping data fetcher task due to closed channel.");
                        break;
                    }
                    last_vehicle_journey = Some(vehicle_journey);
                }
                None => {
                    info!("No valid journey data, sending OffJourney.");
                    if let Err(e) = data_sender.send(OnboardDataMessage::OffJourney).await {
                        warn!(error = %e, "Stopping data fetcher task due to closed channel.");
                        break;
                    }
                    last_vehicle_journey = None;
                }
            }
        } else {
            debug!("No data changes detected, skipping update.");
        }
    }

    Ok(())
}

/// Processes journey data using JourneySimulation
/// 
/// This function handles journey validation and simulation updates.
/// Returns None if the journey is invalid or processing fails.
fn process_journey_data(
    journey_simulation: &mut Option<JourneySimulation>,
    journey: Option<Journey>,
    current_timestamp: i64,
) -> Result<Option<VehicleJourney>, ModificationError> {
    let Some(journey) = journey else {
        // No journey data available
        *journey_simulation = None;
        return Ok(None);
    };

    match journey_simulation {
        Some(simulation) => {
            // Update existing simulation with new journey
            match simulation.update_journey(journey.clone()) {
                Ok(()) => {
                    // Update the simulation's internal time
                    simulation.tick();
                    Ok(Some(simulation.vehicle_journey()))
                }
                Err(e) => {
                    warn!("Failed to update journey simulation: {}", e);
                    *journey_simulation = None;
                    Err(e)
                }
            }
        }
        None => {
            // Create new simulation
            match JourneySimulation::new(journey, current_timestamp) {
                Ok(new_simulation) => {
                    let vehicle_journey = new_simulation.vehicle_journey();
                    *journey_simulation = Some(new_simulation);
                    Ok(Some(vehicle_journey))
                }
                Err(validation_error) => {
                    warn!("Journey validation failed: {}", validation_error);
                    Err(validation_error)
                }
            }
        }
    }
}

/// Fetches trip data for a vehicle and converts it to OnboardData.
#[instrument(skip(client))]
async fn fetch_vehicle_trip(
    client: &mut BaselinkClient<InterceptedService<Channel, BucketInterceptor>>,
    vehicle_id: &str,
) -> Result<Option<Trip>> {
    let request = GetVehicleTripRequest {
        vehicle_id: vehicle_id.to_string(),
    };

    // Call the baselink API
    let response = client
        .get_vehicle_trip(request)
        .await
        .context("Failed to get vehicle trip data from Baselink")?
        .into_inner(); // Get the actual response data

    // Save the trip data from Baselink response for debugging/logging purposes
    if let Some(ref trip) = response.current_trip {
        let trip_debug = format!("{:#?}", trip);
        if let Err(e) = tokio::fs::write("raw/baselink_trip.txt", trip_debug).await {
            warn!(
                "Failed to save Baselink trip data to baselink_trip.txt: {}",
                e
            );
        } else {
            debug!("Successfully saved Baselink trip data to baselink_trip.txt");
        }
    } else {
        debug!("No current trip in Baselink response");
    }

    let trip_json = serde_json::to_string_pretty(&response.current_trip).unwrap();
    if let Err(e) = tokio::fs::write("raw/baselink_trip.json", trip_json).await {
        warn!(
            "Failed to save Baselink trip data to baselink_trip.json: {}",
            e
        );
    } else {
        debug!("Successfully saved Baselink trip data to baselink_trip.json");
    }

    Ok(response.current_trip)
}
