use std::sync::{Arc, Mutex};
use tracing::{info, error};
use anyhow::Result;

use crate::journey_simulation::{JourneySimulation, ModificationError};
use crate::onboard_model::{Journey, LocationStatus, VehicleJourney, CurrentCallStatus};
use crate::http_api::types::{SimulationState, PlaybackState};

/// High-level simulation manager that encapsulates JourneySimulation complexity
#[derive(Clone)]
pub struct SimulationManager {
    inner: Arc<Mutex<Option<JourneySimulation>>>,
}

/// Result of simulation state query
#[derive(Debug, Clone)]
pub struct SimulationStateResult {
    pub journey: Option<Journey>,
    pub current_call_index: usize,
    pub status: LocationStatus,
    pub timestamp: i64,
    pub is_playing: bool,
    pub speed_multiplier: u8,
}

impl SimulationManager {
    /// Creates a new simulation manager
    pub fn new() -> Self {
        Self {

            inner: Arc::new(Mutex::new(None)),
        }
    }

    /// Loads a journey into the simulation
    pub fn load_journey(&self, journey: Journey) -> Result<(), String> {
        let current_timestamp = jiff::Timestamp::now().as_second();
        
        match JourneySimulation::new(journey.clone(), current_timestamp) {
            Ok(simulation) => {
                match self.inner.lock() {
                    Ok(mut guard) => {
                        *guard = Some(simulation);
                        info!("Journey loaded: {}", journey.trip_id);
                        Ok(())
                    }
                    Err(e) => {
                        error!("Failed to lock simulation for journey loading: {}", e);
                        Err("Failed to acquire simulation lock".to_string())
                    }
                }
            }
            Err(e) => {
                error!("Failed to create simulation: {}", e);
                Err(format!("Invalid journey: {}", e))
            }
        }
    }

    /// Gets the current simulation state
    pub fn get_state(&self) -> Result<SimulationStateResult, String> {
        match self.inner.lock() {
            Ok(guard) => {
                match guard.as_ref() {
                    Some(sim) => {
                        Ok(SimulationStateResult {
                            journey: Some(sim.journey().clone()),
                            current_call_index: sim.current_call_index(),
                            status: sim.current_status(),
                            timestamp: sim.timestamp(),
                            is_playing: sim.is_playing(),
                            speed_multiplier: sim.speed_multiplier(),
                        })
                    }
                    None => {
                        Ok(SimulationStateResult {
                            journey: None,
                            current_call_index: 0,
                            status: LocationStatus::BeforeStop,
                            timestamp: 0,
                            is_playing: false,
                            speed_multiplier: 4,
                        })
                    }
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for state query: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Starts playback with optional speed multiplier
    pub fn start_playback(&self, speed_multiplier: Option<u8>) -> Result<(), String> {
        match self.inner.lock() {
            Ok(mut guard) => {
                match guard.as_mut() {
                    Some(sim) => {
                        match sim.start_playback(speed_multiplier) {
                            Ok(()) => {
                                info!("Playback started with speed: {}", sim.speed_multiplier());
                                Ok(())
                            }
                            Err(ModificationError::InvalidSpeedMultiplier) => {
                                Err("Invalid speed multiplier (must be 1-10)".to_string())
                            }
                            Err(e) => {
                                error!("Failed to start playback: {}", e);
                                Err("Failed to start playback".to_string())
                            }
                        }
                    }
                    None => Err("No journey loaded".to_string())
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for playback start: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Stops playback
    pub fn stop_playback(&self) -> Result<(), String> {
        match self.inner.lock() {
            Ok(mut guard) => {
                match guard.as_mut() {
                    Some(sim) => {
                        sim.stop_playback();
                        info!("Playback stopped");
                        Ok(())
                    }
                    None => Err("No journey loaded".to_string())
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for playback stop: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Sets explicit timestamp
    pub fn set_time(&self, timestamp: i64) -> Result<(), String> {
        match self.inner.lock() {
            Ok(mut guard) => {
                match guard.as_mut() {
                    Some(sim) => {
                        sim.set_time(timestamp);
                        info!("Time set to: {}", timestamp);
                        Ok(())
                    }
                    None => Err("No journey loaded".to_string())
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for time set: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Advances to next state
    pub fn next_state(&self) -> Result<(), String> {
        match self.inner.lock() {
            Ok(mut guard) => {
                match guard.as_mut() {
                    Some(sim) => {
                        if sim.next_state() {
                            info!("Advanced to next state at: {}", sim.timestamp());
                            Ok(())
                        } else {
                            Err("Already at last state".to_string())
                        }
                    }
                    None => Err("No journey loaded".to_string())
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for next state: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Moves to previous state
    pub fn previous_state(&self) -> Result<(), String> {
        match self.inner.lock() {
            Ok(mut guard) => {
                match guard.as_mut() {
                    Some(sim) => {
                        if sim.previous_state() {
                            info!("Moved to previous state at: {}", sim.timestamp());
                            Ok(())
                        } else {
                            Err("Already at first state".to_string())
                        }
                    }
                    None => Err("No journey loaded".to_string())
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for previous state: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Advances to next stop
    pub fn next_stop(&self) -> Result<(), String> {
        match self.inner.lock() {
            Ok(mut guard) => {
                match guard.as_mut() {
                    Some(sim) => {
                        if sim.next_stop() {
                            info!("Advanced to next stop at: {}", sim.timestamp());
                            Ok(())
                        } else {
                            Err("Already at last stop".to_string())
                        }
                    }
                    None => Err("No journey loaded".to_string())
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for next stop: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Moves to previous stop
    pub fn previous_stop(&self) -> Result<(), String> {
        match self.inner.lock() {
            Ok(mut guard) => {
                match guard.as_mut() {
                    Some(sim) => {
                        if sim.previous_stop() {
                            info!("Moved to previous stop at: {}", sim.timestamp());
                            Ok(())
                        } else {
                            Err("Already at first stop".to_string())
                        }
                    }
                    None => Err("No journey loaded".to_string())
                }
            }
            Err(e) => {
                error!("Failed to lock simulation for previous stop: {}", e);
                Err("Failed to acquire simulation lock".to_string())
            }
        }
    }

    /// Performs a simulation tick (for playback)
    pub fn tick(&self) {
        if let Ok(mut guard) = self.inner.lock() {
            if let Some(sim) = guard.as_mut() {
                sim.tick();
            }
        }
    }

    /// Gets current journey for data fetcher
    pub fn get_current_journey(&self) -> Option<VehicleJourney> {
        match self.inner.lock() {
            Ok(guard) => {
                guard.as_ref().map(|sim| {
                    VehicleJourney {
                        journey: sim.journey().clone(),
                        current_call_status: Some(CurrentCallStatus {
                            call_index: sim.current_call_index(),
                            status: sim.current_status(),
                        }),
                    }
                })
            }
            Err(e) => {
                error!("Failed to lock simulation for journey query: {}", e);
                None
            }
        }
    }

    /// Checks if a journey is currently loaded
    pub fn has_journey(&self) -> bool {
        match self.inner.lock() {
            Ok(guard) => guard.is_some(),
            Err(_) => false,
        }
    }
}

impl Default for SimulationManager {
    fn default() -> Self {
        Self::new()
    }
}

// Convert to HTTP API response format
impl From<SimulationStateResult> for SimulationState {
    fn from(state: SimulationStateResult) -> Self {
        Self {
            journey: state.journey,
            current_call_index: Some(state.current_call_index),
            status: Some(state.status),
            timestamp: state.timestamp,
            playback_state: if state.is_playing { 
                PlaybackState::Playing 
            } else { 
                PlaybackState::Stopped 
            },
            speed_multiplier: state.speed_multiplier,
        }
    }
} 