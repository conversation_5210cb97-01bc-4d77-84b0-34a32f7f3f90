use baselink_api::{<PERSON><PERSON> as <PERSON><PERSON>Halt, Trip as BaselinkTrip};
use crate::onboard_model::{Call, Journey, ExitSide};
use anyhow::{Context, Result};
use std::path::PathBuf;
use tokio::fs;
use tracing::{warn, instrument};


impl From<BaselinkHalt> for Call {
    fn from(halt: BaselinkHalt) -> Self {
        Call {
            stop_id: halt.stop_id,
            seq_number: halt.seq_number,

            scheduled_arrival_time: Some(halt.scheduled_arrival_time),
            scheduled_departure_time: Some(halt.scheduled_departure_time),
            // Convert 0 to None for actual times (0 means no actual time in baselink API)
            actual_arrival_time: (halt.actual_arrival_time != 0).then_some(halt.actual_arrival_time),
            actual_departure_time: (halt.actual_departure_time != 0).then_some(halt.actual_departure_time),

            destination_text: Some(halt.destination_text),
            platform_name: Some(halt.platform_name),
            stop_name: halt.stop_name,
            advisory_messages: halt.advisory_messages,
            is_cancelled: false,
            exit_side: ExitSide::Unknown,
        }
    }
}

impl From<BaselinkTrip> for Journey {
    fn from(trip: BaselinkTrip) -> Self {
        Self {
            trip_id: trip.id,
            destination_text: trip.destination_text,
            line_name: trip.line_name,
            // Convert each halt in the vector
            calls: trip
                .halts
                .into_iter()
                .map(Call::from)
                .collect(),
        }
    }
}

/// Reads a baselink trip data file and deserializes it into a BaselinkTrip.
#[instrument(fields(file_path = %file_path.display()))]
pub async fn read_baselink_trip_data(file_path: &PathBuf) -> Result<Option<BaselinkTrip>> {
    match fs::read_to_string(file_path).await {
        Ok(json_content) => {
            if json_content.trim().is_empty() {
                warn!("Data file is empty.");
                return Ok(None);
            }
             // Use the wrapper for deserialization
            let wrapper: BaselinkTrip = serde_json::from_str(&json_content)
                .with_context(|| format!("Failed to deserialize JSON from file: {:?}", file_path))?;
            Ok(Some(wrapper))
        }
        Err(e) if e.kind() == std::io::ErrorKind::NotFound => {
            warn!("Data file not found at path: {:?}", file_path);
            Ok(None)
        }
        Err(e) => Err(e).with_context(|| format!("Failed to read data file: {:?}", file_path)),
    }
}
