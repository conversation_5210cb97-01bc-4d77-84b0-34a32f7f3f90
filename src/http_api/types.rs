use serde::{Deserialize, Serialize};
use crate::onboard_model::{Journey, LocationStatus};

// ============================================================================
// HTTP API Response Format
// ============================================================================

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: String,
    pub timestamp: String,
    pub error: Option<ApiError>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ApiError {
    pub code: String,
    pub message: String,
    pub details: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T, message: &str) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: message.to_string(),
            timestamp: jiff::Timestamp::now().to_string(),
            error: None,
        }
    }

    pub fn error(code: &str, message: &str, details: Option<String>) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: message.to_string(),
            timestamp: jiff::Timestamp::now().to_string(),
            error: Some(ApiError {
                code: code.to_string(),
                message: message.to_string(),
                details,
            }),
        }
    }

    pub fn error_with_type<U>(code: &str, message: &str, details: Option<String>) -> ApiResponse<U> {
        ApiResponse {
            success: false,
            data: None,
            message: message.to_string(),
            timestamp: jiff::Timestamp::now().to_string(),
            error: Some(ApiError {
                code: code.to_string(),
                message: message.to_string(),
                details,
            }),
        }
    }
}

// ============================================================================
// Simulation Control API Types
// ============================================================================

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct SimulationState {
    pub journey: Option<Journey>,
    pub current_call_index: Option<usize>,
    pub status: Option<LocationStatus>,
    pub timestamp: i64,
    pub playback_state: PlaybackState,
    pub speed_multiplier: u8,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "lowercase")]
pub enum PlaybackState {
    Playing,
    Stopped,
}

// Request types for simulation control
#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ResumeRequest {
    pub speed_multiplier: Option<u8>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct TimeRequest {
    pub timestamp: i64,
}

// ============================================================================
// Data Loading API Types  
// ============================================================================

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct LoadJourneyRequest {
    pub journey: Journey,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct DataSourceInfo {
    pub current_source: String,
    pub available_sources: Vec<String>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct SwitchSourceRequest {
    pub source: String,
}

// ============================================================================
// Baselink API Types
// ============================================================================

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct VehicleInfo {
    pub id: String,
    pub trip_id: Option<String>,
    pub vehicle_type: Option<String>,
    pub status: String,
}

// TripInfo and HaltInfo removed - using Journey directly with from_baselink.rs converters

#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BaselinkStatus {
    pub connected: bool,
    pub version: Option<String>,
    pub server_url: Option<String>,
    pub startup_time: Option<i64>,
} 