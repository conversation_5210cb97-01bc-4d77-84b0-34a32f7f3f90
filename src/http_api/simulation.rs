use axum::{
    extract::State,
    http::StatusCode,
    response::J<PERSON>,
};
use tracing::{warn, error};


use crate::http_api::types::*;
use crate::http_api::server::ServerState;

// ============================================================================
// Simulation State Endpoints
// ============================================================================

/// GET /api/simulation/state - Get complete simulation state
pub async fn get_simulation_state(
    State(state): State<ServerState>,
) -> Result<Json<ApiResponse<SimulationState>>, StatusCode> {
    match state.simulation_manager.get_state() {
        Ok(state) => {
            let api_state: SimulationState = state.into();
            let message = if api_state.journey.is_some() {
                "Current simulation state"
            } else {
                "No journey loaded"
            };
            Ok(Json(ApiResponse::success(api_state, message)))
        }
        Err(e) => {
            error!("Failed to get simulation state: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

// ============================================================================
// Playback Control Endpoints
// ============================================================================

/// POST /api/simulation/stop - Stop playback
pub async fn stop_playback(
    State(state): State<ServerState>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.simulation_manager.stop_playback() {
        Ok(()) => Ok(Json(ApiResponse::success((), "Playback stopped"))),
        Err(e) => {
            warn!("Failed to stop playback: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// POST /api/simulation/resume - Resume playback with optional speed
pub async fn resume_playback(
    State(state): State<ServerState>,
    Json(request): Json<ResumeRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.simulation_manager.start_playback(request.speed_multiplier) {
        Ok(()) => Ok(Json(ApiResponse::success((), "Playback resumed"))),
        Err(e) => {
            warn!("Failed to resume playback: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

// ============================================================================
// Time Control Endpoints
// ============================================================================

/// POST /api/simulation/time - Set explicit timestamp
pub async fn set_time(
    State(state): State<ServerState>,
    Json(request): Json<TimeRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.simulation_manager.set_time(request.timestamp) {
        Ok(()) => Ok(Json(ApiResponse::success((), "Time updated"))),
        Err(e) => {
            warn!("Failed to set time: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

// ============================================================================
// Navigation Endpoints
// ============================================================================

/// POST /api/simulation/next-state - Jump to next trip state
pub async fn next_state(
    State(state): State<ServerState>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.simulation_manager.next_state() {
        Ok(()) => Ok(Json(ApiResponse::success((), "Advanced to next state"))),
        Err(e) => {
            warn!("Failed to advance to next state: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// POST /api/simulation/previous-state - Jump to previous trip state
pub async fn previous_state(
    State(state): State<ServerState>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.simulation_manager.previous_state() {
        Ok(()) => Ok(Json(ApiResponse::success((), "Moved to previous state"))),
        Err(e) => {
            warn!("Failed to move to previous state: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// POST /api/simulation/next-stop - Jump to next stop
pub async fn next_stop(
    State(state): State<ServerState>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.simulation_manager.next_stop() {
        Ok(()) => Ok(Json(ApiResponse::success((), "Advanced to next stop"))),
        Err(e) => {
            warn!("Failed to advance to next stop: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

/// POST /api/simulation/previous-stop - Jump to previous stop
pub async fn previous_stop(
    State(state): State<ServerState>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    match state.simulation_manager.previous_stop() {
        Ok(()) => Ok(Json(ApiResponse::success((), "Moved to previous stop"))),
        Err(e) => {
            warn!("Failed to move to previous stop: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
} 