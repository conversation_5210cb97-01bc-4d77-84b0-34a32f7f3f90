use axum::{
    extract::State,
    http::StatusCode,
    response::J<PERSON>,
};
use tracing::{info, warn};


use crate::http_api::types::*;
use crate::http_api::server::ServerState;

// ============================================================================
// Journey Loading Endpoints
// ============================================================================

/// POST /api/data/load/journey - Load journey data into simulation
pub async fn load_journey(
    State(state): State<ServerState>,
    Json(request): Json<LoadJourneyRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    info!("Loading journey: {}", request.journey.trip_id);
    
    match state.simulation_manager.load_journey(request.journey) {
        Ok(()) => Ok(Json(ApiResponse::success((), "Journey loaded successfully"))),
        Err(e) => {
            warn!("Failed to load journey: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

// ============================================================================
// Data Source Management Endpoints
// ============================================================================

/// GET /api/data/sources - Get available data sources and current source
pub async fn get_data_sources() -> Result<Json<ApiResponse<DataSourceInfo>>, StatusCode> {
    let data_source_info = DataSourceInfo {
        current_source: "http".to_string(), // Since we're in HTTP mode
        available_sources: vec![
            "http".to_string(),
            "baselink".to_string(),
            "file".to_string(),
            "manual".to_string(),
        ],
    };
    
    Ok(Json(ApiResponse::success(data_source_info, "Data sources retrieved")))
}

/// POST /api/data/source/switch - Switch between data sources (placeholder)
pub async fn switch_data_source(
    Json(_request): Json<SwitchSourceRequest>,
) -> Result<Json<ApiResponse<()>>, StatusCode> {
    // This is a placeholder - in a real implementation, this would trigger
    // a restart of the application with a different data source
    warn!("Data source switching not implemented - requires application restart");
    Err(StatusCode::NOT_IMPLEMENTED)
} 