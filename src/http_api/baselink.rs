use axum::{
    extract::{State, Path},
    http::StatusCode,
    response::{<PERSON><PERSON>, IntoResponse},
};
use tracing::{error, info};

use crate::http_api::{
    types::{ApiResponse, ApiError, VehicleInfo, BaselinkStatus},
    server::ServerState,
};
use crate::onboard_model::Journey;

/// Get baselink connection status
pub async fn get_baselink_status(
    State(state): State<ServerState>,
) -> impl IntoResponse {
    match &state.baselink_api {
        Some(baselink_api) => {
            // Try to get version to check if connection is alive
            match baselink_api.get_version().await {
                Ok(version) => {
                    let startup_time = match baselink_api.get_startup_time().await {
                        Ok(time) => Some(time),
                        Err(e) => {
                            error!("Failed to get startup time: {}", e);
                            None
                        }
                    };

                    let status = BaselinkStatus {
                        connected: true,
                        version: Some(format!("{}.{}.{}", version.major, version.minor, version.patch)),
                        server_url: None, // We don't store the URL in the client
                        startup_time,
                    };

                    (StatusCode::OK, Json(ApiResponse::success(
                        status,
                        "Baselink connection status retrieved successfully",
                    ))).into_response()
                }
                Err(e) => {
                    error!("Failed to get baselink version: {}", e);
                    let status = BaselinkStatus {
                        connected: false,
                        version: None,
                        server_url: None,
                        startup_time: None,
                    };

                    (StatusCode::OK, Json(ApiResponse::success(
                        status,
                        "Baselink connection failed",
                    ))).into_response()
                }
            }
        }
        None => {
            let error_response: ApiResponse<BaselinkStatus> = ApiResponse {
                success: false,
                data: None,
                message: "Baselink API is not configured".to_string(),
                timestamp: jiff::Timestamp::now().to_string(),
                error: Some(ApiError {
                    code: "BASELINK_NOT_AVAILABLE".to_string(),
                    message: "Baselink API is not configured".to_string(),
                    details: Some("Server was not started with baselink data source".to_string()),
                }),
            };
            (StatusCode::SERVICE_UNAVAILABLE, Json(error_response)).into_response()
        }
    }
}

/// Get list of available vehicles with their current trip IDs
pub async fn get_vehicles(
    State(state): State<ServerState>,
) -> impl IntoResponse {
    match &state.baselink_api {
        Some(baselink_api) => {
            match baselink_api.get_vehicles().await {
                Ok(vehicles) => {
                    let vehicle_infos: Vec<VehicleInfo> = vehicles
                        .into_iter()
                        .map(|vehicle| VehicleInfo {
                            id: vehicle.id,
                            trip_id: Some(vehicle.trip_id),
                            vehicle_type: None, // Vehicle type not available in baselink API
                            status: "active".to_string(), // Baselink vehicles are typically active
                        })
                        .collect();

                    info!("Retrieved {} vehicles from baselink", vehicle_infos.len());
                    (StatusCode::OK, Json(ApiResponse::success(
                        vehicle_infos,
                        "Vehicles retrieved successfully",
                    )))
                }
                Err(e) => {
                    error!("Failed to get vehicles from baselink: {}", e);
                    let error_response: ApiResponse<Vec<VehicleInfo>> = ApiResponse {
                        success: false,
                        data: None,
                        message: "Failed to retrieve vehicles from baselink".to_string(),
                        timestamp: jiff::Timestamp::now().to_string(),
                        error: Some(ApiError {
                            code: "BASELINK_COMMUNICATION_ERROR".to_string(),
                            message: "Failed to retrieve vehicles from baselink".to_string(),
                            details: Some(e.to_string()),
                        }),
                    };
                    (StatusCode::BAD_GATEWAY, Json(error_response))
                }
            }
        }
        None => {
            let error_response: ApiResponse<Vec<VehicleInfo>> = ApiResponse {
                success: false,
                data: None,
                message: "Baselink API is not configured".to_string(),
                timestamp: jiff::Timestamp::now().to_string(),
                error: Some(ApiError {
                    code: "BASELINK_NOT_AVAILABLE".to_string(),
                    message: "Baselink API is not configured".to_string(),
                    details: Some("Server was not started with baselink data source".to_string()),
                }),
            };
            (StatusCode::SERVICE_UNAVAILABLE, Json(error_response))
        }
    }
}

/// Get trip information by trip ID
pub async fn get_trip(
    State(state): State<ServerState>,
    Path(trip_id): Path<String>,
) -> impl IntoResponse {
    match &state.baselink_api {
        Some(baselink_api) => {
            match baselink_api.get_trip(&trip_id).await {
                Ok(Some(trip)) => {
                    // Convert baselink Trip to Journey using the From implementation
                    let journey = Journey::from(trip);

                    info!("Retrieved trip {} from baselink", trip_id);
                    (StatusCode::OK, Json(ApiResponse::success(
                        journey,
                        "Trip data retrieved successfully",
                    )))
                }
                Ok(None) => {
                    let error_response: ApiResponse<Journey> = ApiResponse {
                        success: false,
                        data: None,
                        message: format!("Trip with ID '{}' not found", trip_id),
                        timestamp: jiff::Timestamp::now().to_string(),
                        error: Some(ApiError {
                            code: "TRIP_NOT_FOUND".to_string(),
                            message: format!("Trip with ID '{}' not found", trip_id),
                            details: Some("Trip may not be active or may not exist".to_string()),
                        }),
                    };
                    (StatusCode::NOT_FOUND, Json(error_response))
                }
                Err(e) => {
                    error!("Failed to get trip from baselink: {}", e);
                    let error_response: ApiResponse<Journey> = ApiResponse {
                        success: false,
                        data: None,
                        message: "Failed to retrieve trip from baselink".to_string(),
                        timestamp: jiff::Timestamp::now().to_string(),
                        error: Some(ApiError {
                            code: "BASELINK_COMMUNICATION_ERROR".to_string(),
                            message: "Failed to retrieve trip from baselink".to_string(),
                            details: Some(e.to_string()),
                        }),
                    };
                    (StatusCode::BAD_GATEWAY, Json(error_response))
                }
            }
        }
        None => {
            let error_response: ApiResponse<Journey> = ApiResponse {
                success: false,
                data: None,
                message: "Baselink API is not configured".to_string(),
                timestamp: jiff::Timestamp::now().to_string(),
                error: Some(ApiError {
                    code: "BASELINK_NOT_AVAILABLE".to_string(),
                    message: "Baselink API is not configured".to_string(),
                    details: Some("Server was not started with baselink data source".to_string()),
                }),
            };
            (StatusCode::SERVICE_UNAVAILABLE, Json(error_response))
        }
    }
} 