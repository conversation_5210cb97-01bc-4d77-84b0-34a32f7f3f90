use axum::{
    routing::{get, post},
    Router,
};
use tower::ServiceBuilder;
use tower_http::cors::{CorsLayer, Any};
use std::sync::Arc;

use tracing::info;

use crate::http_api::{simulation, data}; // baselink temporarily disabled
use crate::simulation_manager::SimulationManager;
use crate::baselink_api_client::BaselinkApi;

/// Shared server state containing simulation manager and optional baselink API
#[derive(Clone)]
pub struct ServerState {
    pub simulation_manager: SimulationManager,
    pub baselink_api: Option<Arc<BaselinkApi>>,
}

impl ServerState {
    pub fn new(simulation_manager: SimulationManager, baselink_api: Option<BaselinkApi>) -> Self {
        Self {
            simulation_manager,
            baselink_api: baselink_api.map(Arc::new),
        }
    }
}

/// Creates the HTTP API server with all routes configured
pub fn create_http_server(baselink_api: Option<BaselinkApi>) -> (Router, ServerState) {
    // Create shared simulation state
    let simulation_manager = SimulationManager::new();
    let server_state = ServerState::new(simulation_manager, baselink_api);
    
    // Create CORS layer for development
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(Any)
        .allow_headers(Any);

    // Build the router with all API endpoints
    let app = Router::new()
        // Simulation Control endpoints
        .route("/api/simulation/state", get(simulation::get_simulation_state))
        .route("/api/simulation/stop", post(simulation::stop_playback))
        .route("/api/simulation/resume", post(simulation::resume_playback))
        .route("/api/simulation/time", post(simulation::set_time))
        .route("/api/simulation/next-state", post(simulation::next_state))
        .route("/api/simulation/previous-state", post(simulation::previous_state))
        .route("/api/simulation/next-stop", post(simulation::next_stop))
        .route("/api/simulation/previous-stop", post(simulation::previous_stop))
        
        // Data Loading endpoints
        .route("/api/data/load/journey", post(data::load_journey))
        .route("/api/data/sources", get(data::get_data_sources))
        .route("/api/data/source/switch", post(data::switch_data_source))
        
        // Baselink endpoints
        .route("/api/baselink/status", get(crate::http_api::baselink::get_baselink_status))
        .route("/api/baselink/vehicles", get(crate::http_api::baselink::get_vehicles))
        .route("/api/baselink/trips/:trip_id", get(crate::http_api::baselink::get_trip))
        
        // Add shared server state to all routes
        .with_state(server_state.clone())
        
        // Add middleware
        .layer(
            ServiceBuilder::new()
                .layer(cors)
                .into_inner(),
        );

    info!("HTTP API server configured with simulation, data, and baselink endpoints");
    (app, server_state)
} 