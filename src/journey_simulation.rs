use crate::journey_timeline::{JourneyTimeline, TimelineError, TimePoint};
use crate::onboard_model::{Journey, LocationStatus, VehicleJourney};

/// Error types for onboard modifications
#[derive(Debug, PartialEq)]
pub enum ModificationError {

    InvalidSpeedMultiplier,
    TimelineError(TimelineError),
}

/// Main structure for onboard data modifications
pub struct JourneySimulation {
    journey: Journey,
    // Timeline calculator for timepoint navigation
    timeline: JourneyTimeline,

    current_timepoint: TimePoint,
    // The actual simulation timestamp (can be different from current_timepoint.timestamp)
    current_timestamp: i64,
    // timestamp of the RTC clock on last update of a timestamp (in seconds)
    // this is used to calculate the offset of the timestamp from the RTC clock
    rtc_timestamp: i64,
    speed_factor: u8,
    is_playing: bool,
}

impl JourneySimulation {
    /// Creates a new OnboardModifications instance
    pub fn new(journey: Journey, timestamp: i64) -> Result<Self, ModificationError> {
        let timeline = JourneyTimeline::from_journey(&journey)
            .map_err(ModificationError::TimelineError)?;
        let current_timepoint = timeline.find_current(timestamp);
        Ok(Self {
            journey,
            timeline,
            current_timepoint,
            current_timestamp: timestamp,
            rtc_timestamp: jiff::Timestamp::now().as_second(),
            speed_factor: 4,
            is_playing: false,
        })
    }


    pub fn vehicle_journey(&self) -> VehicleJourney {
        VehicleJourney {
            journey: self.journey.clone(),
            current_call_status: None,
        }
    }

    pub fn update_journey(&mut self, journey: Journey) -> Result<(), ModificationError> {
        let new_timeline = JourneyTimeline::from_journey(&journey)
            .map_err(ModificationError::TimelineError)?;
        // Find the current timepoint in the new timeline
        self.current_timepoint = new_timeline.find_current(self.current_timestamp);
        self.timeline = new_timeline;
        self.journey = journey;
        Ok(())
    }

    /// Gets the current trip status
    pub fn current_status(&self) -> LocationStatus {
        self.current_timepoint.status.clone()
    }

    /// Gets the current call index
    pub fn current_call_index(&self) -> usize {
        self.current_timepoint.call_index
    }

    /// Gets the current timestamp
    pub fn timestamp(&self) -> i64 {
        self.current_timestamp
    }

    /// Gets a reference to the journey if it exists
    pub fn journey(&self) -> &Journey {
        &self.journey
    }

    /// Gets a reference to the current timepoint
    pub fn current_timepoint(&self) -> &TimePoint {
        &self.current_timepoint
    }

    /// Sets the timestamp to a specific value
    pub fn set_time(&mut self, timestamp: i64) {
        self.current_timepoint = self.timeline.find_current(timestamp);
        self.current_timestamp = timestamp; // Store the exact timestamp requested
        self.rtc_timestamp = jiff::Timestamp::now().as_second();
    }

    /// Gets the current speed multiplier
    pub fn speed_multiplier(&self) -> u8 {
        self.speed_factor
    }

    /// Sets the speed multiplier with validation (1-10 range)
    pub fn set_speed_multiplier(&mut self, speed: u8) -> Result<(), ModificationError> {
        if speed < 1 || speed > 10 {
            return Err(ModificationError::InvalidSpeedMultiplier);
        }
        self.speed_factor = speed;
        Ok(())
    }

    /// Gets the current playback state
    pub fn is_playing(&self) -> bool {
        self.is_playing
    }

    /// Starts playback with optional speed multiplier
    pub fn start_playback(&mut self, speed_multiplier: Option<u8>) -> Result<(), ModificationError> {
        if let Some(speed) = speed_multiplier {
            self.set_speed_multiplier(speed)?;
        }
        self.is_playing = true;
        self.rtc_timestamp = jiff::Timestamp::now().as_second();
        Ok(())
    }

    /// Stops playback (pause and stop are the same)
    pub fn stop_playback(&mut self) {
        self.is_playing = false;
    }

    /// Time step based on rtc_timestamp.
    /// given the rtc_timestamp, update the timestamp based on the speed factor.
    /// This is used to simulate the vehicle journey in real time.
    pub fn tick(&mut self) {
        if !self.is_playing {
            return; // Don't advance time if not playing
        }
        let now = jiff::Timestamp::now().as_second();
        let diff = now - self.rtc_timestamp;
        self.rtc_timestamp = now;
        self.set_time(self.current_timestamp + diff * self.speed_factor as i64);
    }

    /// Navigates to the next stop. Returns true if successful, false if already at end.
    /// Updates the internal timestamp to the next stop.
    pub fn next_stop(&mut self) -> bool {
        if let Some(timepoint) = self.timeline.find_next_stop(self.current_timestamp) {
            self.current_timepoint = timepoint.clone();
            self.current_timestamp = timepoint.timestamp;
            self.rtc_timestamp = jiff::Timestamp::now().as_second();
            true
        } else {
            false
        }
    }

    /// Navigates to the previous stop. Returns true if successful, false if already at beginning.
    /// Updates the internal timestamp to the previous stop.
    pub fn previous_stop(&mut self) -> bool {
        if let Some(timepoint) = self.timeline.find_previous_stop(self.current_timestamp) {
            self.current_timepoint = timepoint.clone();
            self.current_timestamp = timepoint.timestamp;
            self.rtc_timestamp = jiff::Timestamp::now().as_second();
            true
        } else {
            false
        }
    }

    /// Navigates to the next state in the journey timeline. 
    /// Returns true if successful, false if at the end.
    /// Updates the internal timepoint to the next timepoint.
    /// Uses precise timepoint-based navigation to avoid stuck states.
    pub fn next_state(&mut self) -> bool {
        if let Some(timepoint) = self.timeline.next_state_from_timepoint(&self.current_timepoint) {
            self.current_timepoint = timepoint.clone();
            self.current_timestamp = timepoint.timestamp;
            self.rtc_timestamp = jiff::Timestamp::now().as_second();
            true
        } else {
            false
        }
    }

    /// Navigates to the previous state in the journey timeline.
    /// Returns true if successful, false if at the beginning.
    /// Updates the internal timepoint to the previous timepoint.
    /// Uses precise timepoint-based navigation to avoid stuck states.
    pub fn previous_state(&mut self) -> bool {
        if let Some(timepoint) = self.timeline.previous_state_from_timepoint(&self.current_timepoint) {
            self.current_timepoint = timepoint.clone();
            self.current_timestamp = timepoint.timestamp;
            self.rtc_timestamp = jiff::Timestamp::now().as_second();
            true
        } else {
            false
        }
    }
}

impl std::fmt::Display for ModificationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ModificationError::InvalidSpeedMultiplier => {
                write!(f, "Invalid speed multiplier (must be between 1 and 10)")
            }
            ModificationError::TimelineError(e) => write!(f, "Timeline error: {}", e),
        }
    }
}

impl std::error::Error for ModificationError {}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::onboard_model::{Call, ExitSide};

    fn create_test_call(
        stop_id: &str,
        seq_number: u32,
        scheduled_arrival: i64,
        scheduled_departure: i64,
        actual_arrival: Option<i64>,
        actual_departure: Option<i64>,
    ) -> Call {
        Call {
            stop_id: stop_id.to_string(),
            seq_number,
            scheduled_arrival_time: Some(scheduled_arrival),
            scheduled_departure_time: Some(scheduled_departure),
            actual_arrival_time: actual_arrival,
            actual_departure_time: actual_departure,
            destination_text: Some("Test Destination".to_string()),
            platform_name: Some("Platform 1".to_string()),
            stop_name: format!("Test Stop {}", seq_number),
            advisory_messages: vec![],
            is_cancelled: false,
            exit_side: ExitSide::Unknown,
        }
    }

    fn create_test_journey() -> Journey {
        Journey {
            trip_id: "test-trip".to_string(),
            destination_text: "Test Destination".to_string(),
            line_name: "Test Line".to_string(),
            calls: vec![
                create_test_call("stop1", 1, 1000, 1100, Some(1000), Some(1100)),
                create_test_call("stop2", 2, 1200, 1300, None, None),
                create_test_call("stop3", 3, 1400, 1500, None, None),
            ],
        }
    }

    #[test]
    fn test_journey_simulation_creation() {
        let simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        assert_eq!(simulation.timestamp(), 1000);
        assert_eq!(simulation.journey().calls.len(), 3);
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
        assert_eq!(simulation.current_call_index(), 0);
    }

    #[test]
    fn test_manual_time_update() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Record initial state
        let _initial_status = simulation.current_status();
        let initial_call_index = simulation.current_call_index();
        
        // Update time manually - this will find the timepoint active at 1250
        simulation.set_time(1250);
        // The timestamp might not be exactly 1250 as it finds the timepoint active at that time
        let new_timestamp = simulation.timestamp();
        assert!(new_timestamp <= 1250, "Timestamp should be the timepoint active at or before 1250");
        
        // Status should have updated
        let new_status = simulation.current_status();
        let new_call_index = simulation.current_call_index();
        
        // Should be different since we moved in time
        assert!(new_timestamp != 1000 || new_status != _initial_status || new_call_index != initial_call_index,
                "Time, status, or call index should have changed");
    }

    #[test]
    fn test_next_stop_navigation() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Start at first stop
        simulation.set_time(1000); // At first stop arrival
        let initial_timestamp = simulation.timestamp();
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
        
        // Navigate to next stop
        let success = simulation.next_stop();
        assert!(success, "Should successfully navigate to next stop");
        
        // Should have moved in time
        assert!(simulation.timestamp() > initial_timestamp);
        // Should be at a stop (same status but different location)
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
    }

    #[test]
    fn test_previous_stop_navigation() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Start at second stop
        simulation.set_time(1200); // At second stop arrival
        let initial_timestamp = simulation.timestamp();
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
        
        // Navigate to previous stop
        let success = simulation.previous_stop();
        assert!(success, "Should successfully navigate to previous stop");
        
        // Should have moved back in time
        assert!(simulation.timestamp() < initial_timestamp);
        // Should be at a stop (same status but different location)
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
    }

    #[test]
    fn test_next_state_navigation() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Start at a specific point
        simulation.set_time(970); // Before first stop
        let initial_timestamp = simulation.timestamp();
        let initial_status = simulation.current_status();
        
        // Navigate to next state
        let success = simulation.next_state();
        assert!(success, "Should successfully navigate to next state");
        
        // Should have moved forward in time
        assert!(simulation.timestamp() > initial_timestamp);
        // Status may or may not change (depending on timepoint sequence)
        // so expect the same status or next status
        let new_status = simulation.current_status();
        println!("new_status: {:?}", new_status);
        assert!(new_status == initial_status || new_status == LocationStatus::AtStop);
    }

    #[test]
    fn test_previous_state_navigation() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Start at a later point in the journey
        simulation.set_time(1250); // At second stop
        let initial_timestamp = simulation.timestamp();
        let initial_status = simulation.current_status();
        
        // Navigate to previous state
        let success = simulation.previous_state();
        assert!(success, "Should successfully navigate to previous state");
        
        // Should have moved back in time
        assert!(simulation.timestamp() < initial_timestamp);
        // and status should be the same
        assert_eq!(simulation.current_status(), initial_status.previous());
    }

    #[test]
    fn test_navigation_sequence() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Start at beginning
        simulation.set_time(970);
        let mut timestamps = vec![simulation.timestamp()];
        
        // Navigate forward through several states
        for _ in 0..5 {
            if simulation.next_state() {
                timestamps.push(simulation.timestamp());
            } else {
                break;
            }
        }
        
        // Should have moved forward monotonically
        assert!(timestamps.len() > 1, "Should have navigated through multiple states");
        for window in timestamps.windows(2) {
            assert!(window[1] > window[0], "Should move forward in time");
        }
    }

    #[test]
    fn test_navigation_boundary_conditions() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Navigate to the very end
        simulation.set_time(2000); // Way past the end
        let can_go_next = simulation.next_state();
        assert!(!can_go_next, "Should not be able to navigate past the end");
        
        // Navigate to the very beginning
        simulation.set_time(900); // Before the start
        let can_go_previous = simulation.previous_state();
        assert!(!can_go_previous, "Should not be able to navigate before the start");
    }

    #[test]
    fn test_stop_navigation_cycle() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Start at first stop
        simulation.set_time(1000);
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
        let start_timestamp = simulation.timestamp();
        
        // Go forward to next stop
        assert!(simulation.next_stop(), "Should navigate to next stop");
        let middle_timestamp = simulation.timestamp();
        assert!(middle_timestamp > start_timestamp);
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
        
        // Go back to previous stop
        assert!(simulation.previous_stop(), "Should navigate back to previous stop");
        let end_timestamp = simulation.timestamp();
        assert!(end_timestamp < middle_timestamp);
        assert_eq!(simulation.current_status(), LocationStatus::AtStop);
    }

    #[test]
    fn test_state_navigation_preserves_consistency() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Test that navigation always results in valid states
        simulation.set_time(1050); // Start at a known good state
        
        for _ in 0..10 {
            let before_timestamp = simulation.timestamp();
            let current_status = simulation.current_status();
            let current_call_index = simulation.current_call_index();
            if simulation.next_state() {
                assert!(simulation.current_call_index() >= current_call_index);
                assert_eq!(simulation.current_status(), current_status.next());
                assert!(simulation.timestamp() >= before_timestamp);
            } else {
                // Reached the end
                break;
            }
        }
    }

    #[test]
    fn test_navigation_with_invalid_journey() {
        // Test with insufficient calls
        let empty_journey = Journey {
            trip_id: "empty".to_string(),
            destination_text: "Empty".to_string(),
            line_name: "Empty Line".to_string(),
            calls: vec![],
        };
        
        let result = JourneySimulation::new(empty_journey, 1000);
        assert!(result.is_err(), "Should fail to create simulation with empty journey");
    }

    #[test]
    fn test_realistic_journey_navigation() {
        // Create a larger journey to test more realistic scenarios
        let mut calls = vec![];
        let base_time = 1700000000i64;
        
        for i in 0..10 {
            let arrival_time = base_time + (i * 300); // 5 minutes between stops
            let departure_time = arrival_time + 60; // 1 minute dwell time
            calls.push(create_test_call(
                &format!("stop{}", i),
                i as u32,
                arrival_time,
                departure_time,
                None,
                None,
            ));
        }

        let large_journey = Journey {
            trip_id: "realistic-trip".to_string(),
            destination_text: "Final Destination".to_string(),
            line_name: "Express Line".to_string(),
            calls,
        };

        let mut simulation = JourneySimulation::new(large_journey, base_time).unwrap();
        
        // Test navigation through multiple stops
        let mut stop_count = 0;
        while simulation.next_stop() && stop_count < 5 {
            assert_eq!(simulation.current_status(), LocationStatus::AtStop);
            stop_count += 1;
        }
        
        assert!(stop_count > 0, "Should have navigated through multiple stops");
    }

    #[test]
    fn test_journey_update() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        let original_call_count = simulation.journey().calls.len();
        
        // Create a new journey with different calls
        let new_journey = Journey {
            trip_id: "updated-trip".to_string(),
            destination_text: "Updated Destination".to_string(),
            line_name: "Updated Line".to_string(),
            calls: vec![
                create_test_call("new_stop1", 1, 2000, 2100, None, None),
                create_test_call("new_stop2", 2, 2200, 2300, None, None),
            ],
        };
        
        let result = simulation.update_journey(new_journey);
        assert!(result.is_ok(), "Should successfully update journey");
        
        // Verify the journey was updated
        assert_ne!(simulation.journey().calls.len(), original_call_count);
        assert_eq!(simulation.journey().trip_id, "updated-trip");
    }

    #[test]
    fn test_speed_multiplier_validation() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Test valid speed multipliers
        assert!(simulation.set_speed_multiplier(1).is_ok());
        assert_eq!(simulation.speed_multiplier(), 1);
        
        assert!(simulation.set_speed_multiplier(5).is_ok());
        assert_eq!(simulation.speed_multiplier(), 5);
        
        assert!(simulation.set_speed_multiplier(10).is_ok());
        assert_eq!(simulation.speed_multiplier(), 10);
        
        // Test invalid speed multipliers
        assert_eq!(
            simulation.set_speed_multiplier(0),
            Err(ModificationError::InvalidSpeedMultiplier)
        );
        
        assert_eq!(
            simulation.set_speed_multiplier(11),
            Err(ModificationError::InvalidSpeedMultiplier)
        );
        
        // Speed should remain unchanged after invalid attempts
        assert_eq!(simulation.speed_multiplier(), 10);
    }

    #[test]
    fn test_playback_control() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Initial state should be stopped
        assert!(!simulation.is_playing());
        
        // Start playback
        assert!(simulation.start_playback(None).is_ok());
        assert!(simulation.is_playing());
        
        // Start playback with speed multiplier
        assert!(simulation.start_playback(Some(3)).is_ok());
        assert!(simulation.is_playing());
        assert_eq!(simulation.speed_multiplier(), 3);
        
        // Test invalid speed multiplier during start
        assert_eq!(
            simulation.start_playback(Some(15)),
            Err(ModificationError::InvalidSpeedMultiplier)
        );
        
        // Stop playback
        simulation.stop_playback();
        assert!(!simulation.is_playing());
    }

    #[test]
    fn test_tick_with_playback_control() {
        let mut simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        simulation.set_time(1000);
        
        let initial_timestamp = simulation.timestamp();
        
        // Tick should not advance time when stopped
        simulation.stop_playback();
        std::thread::sleep(std::time::Duration::from_millis(100));
        simulation.tick();
        assert_eq!(simulation.timestamp(), initial_timestamp);
        
        // Tick should advance time when playing
        simulation.start_playback(Some(2)).unwrap();
        std::thread::sleep(std::time::Duration::from_millis(100));
        simulation.tick();
        // Note: Due to timing sensitivity, we just check that time can advance
        // The exact amount depends on system timing
    }

    #[test]
    fn test_default_values() {
        let simulation = JourneySimulation::new(create_test_journey(), 1000).unwrap();
        
        // Check default values
        assert_eq!(simulation.speed_multiplier(), 4);
        assert!(!simulation.is_playing());
    }

    #[test]
    fn test_navigation_with_identical_arrival_departure_times() {
        // This test reproduces the bug where previous_state() gets stuck
        // when arrival and departure times are identical
        let calls = vec![
            create_test_call("stop1", 1, 1750570600, 1750570600, None, Some(1750570600)),
            create_test_call("stop2", 2, 1750570686, 1750570686, None, Some(1750570686)), // Same arrival/departure
            create_test_call("stop3", 3, 1750570758, 1750570758, None, Some(1750570758)), // Same arrival/departure
        ];

        let journey = Journey {
            trip_id: "stuck-navigation-test".to_string(),
            destination_text: "Test Destination".to_string(),
            line_name: "Test Line".to_string(),
            calls,
        };

        let mut simulation = JourneySimulation::new(journey, 1750570686).unwrap();
        
        // Set time to the problematic timestamp (stop 2)
        simulation.set_time(1750570686);
        
        // Current state should be at stop 2
        assert_eq!(simulation.current_call_index(), 1); // 0-indexed, so stop 2 is index 1
        assert_eq!(simulation.timestamp(), 1750570686);
        
        // Record initial state
        let initial_timestamp = simulation.timestamp();
        let initial_call_index = simulation.current_call_index();
        
        // Try to move to previous state multiple times
        let mut successful_moves = 0;
        let mut timestamps = vec![initial_timestamp];
        
        for i in 0..5 {
            let before_timestamp = simulation.timestamp();
            let before_call_index = simulation.current_call_index();
            let before_status = simulation.current_status();
            
            let success = simulation.previous_state();
            
            let after_timestamp = simulation.timestamp();
            let after_call_index = simulation.current_call_index();
            let after_status = simulation.current_status();
            
            println!("Attempt {}: success={}, before={}, after={}", i, success, before_timestamp, after_timestamp);
            
            if success {
                successful_moves += 1;
                timestamps.push(after_timestamp);
                
                // Should make meaningful progress (either change timestamp or state)
                assert!(after_timestamp != before_timestamp || 
                        after_call_index != before_call_index || 
                        after_status != before_status, 
                    "previous_state() should make progress, but no change from timestamp={}, call_index={}, status={:?}", 
                    before_timestamp, before_call_index, before_status);
            } else {
                println!("Navigation failed at attempt {}", i);
                break;
            }
        }
        
        println!("Successful moves: {}", successful_moves);
        println!("Timestamps: {:?}", timestamps);
        
        // We should be able to move at least once (unless we started at the very beginning)
        if initial_call_index > 0 {
            assert!(successful_moves > 0, "Should be able to move to previous state when not at the beginning");
        }
    }

    #[test] 
    fn test_navigation_with_real_problematic_data() {
        // Recreate the exact scenario from the bug report
        let calls = vec![
            // Previous stop
            create_test_call("stop19", 19, 1750570608, 1750570608, None, Some(1750570608)),
            // Problematic stop where we get stuck
            create_test_call("stop20", 20, 1750570686, 1750570686, None, Some(1750570686)),
            // Next stop  
            create_test_call("stop21", 21, 1750570758, 1750570758, None, Some(1750570758)),
        ];

        let journey = Journey {
            trip_id: "real-bug-test".to_string(),
            destination_text: "Test Destination".to_string(),
            line_name: "Test Line".to_string(),
            calls,
        };

        let mut simulation = JourneySimulation::new(journey, 1750570686).unwrap();
        
        // Start at the exact problematic state
        simulation.set_time(1750570686);
        println!("Initial state: timestamp={}, call_index={}, status={:?}", 
            simulation.timestamp(), simulation.current_call_index(), simulation.current_status());
        
        // Try multiple previous_state() calls like in the logs
        for i in 0..10 {
            let before_timestamp = simulation.timestamp();
            let before_call_index = simulation.current_call_index();
            let before_status = simulation.current_status();
            
            let success = simulation.previous_state();
            
            let after_timestamp = simulation.timestamp();
            let after_call_index = simulation.current_call_index();
            let after_status = simulation.current_status();
            
            println!("Move {}: success={}", i, success);
            println!("  Before: timestamp={}, call_index={}, status={:?}", before_timestamp, before_call_index, before_status);
            println!("  After:  timestamp={}, call_index={}, status={:?}", after_timestamp, after_call_index, after_status);
            
            if !success {
                println!("Navigation ended at move {}", i);
                break;
            }
            
            // Check if we made meaningful progress (different state or different timestamp)
            if after_timestamp == before_timestamp && 
               after_call_index == before_call_index && 
               after_status == before_status {
                panic!("BUG REPRODUCED: Stuck at timestamp {} on move {} with no state change", after_timestamp, i);
            }
            
            // We should either move backwards in time or change state meaningfully
            assert!(after_timestamp < before_timestamp || 
                    after_call_index != before_call_index || 
                    after_status != before_status, 
                "previous_state() should make progress, but no meaningful change detected");
        }
    }

    #[test] 
    fn test_examine_duplicate_timepoints() {
        use crate::journey_timeline::JourneyTimeline;
        
        // Create journey with identical arrival/departure times
        let calls = vec![
            create_test_call("stop1", 1, 1750570600, 1750570600, None, Some(1750570600)),
            create_test_call("stop2", 2, 1750570686, 1750570686, None, Some(1750570686)), // Problematic stop
            create_test_call("stop3", 3, 1750570758, 1750570758, None, Some(1750570758)),
        ];

        let journey = Journey {
            trip_id: "examine-timepoints-test".to_string(),
            destination_text: "Test Destination".to_string(),
            line_name: "Test Line".to_string(),
            calls,
        };

        let timeline = JourneyTimeline::from_journey(&journey).unwrap();
        let timepoints = timeline.timepoints();
        
        println!("Generated timepoints:");
        for (i, tp) in timepoints.iter().enumerate() {
            println!("  {}: timestamp={}, status={:?}, call_index={}", 
                i, tp.timestamp, tp.status, tp.call_index);
        }
        
        // Find the problematic timestamp
        let problematic_timestamp = 1750570686;
        let duplicates: Vec<_> = timepoints.iter()
            .enumerate()
            .filter(|(_, tp)| tp.timestamp == problematic_timestamp)
            .collect();
            
        println!("Timepoints with timestamp {}:", problematic_timestamp);
        for (i, tp) in duplicates {
            println!("  Index {}: status={:?}, call_index={}", i, tp.status, tp.call_index);
        }
        
        // Test the previous_state logic step by step
        println!("\nTesting previous_state logic:");
        let mut current_index = None;
        for (i, timepoint) in timepoints.iter().enumerate() {
            if timepoint.timestamp <= problematic_timestamp {
                current_index = Some(i);
                println!("  Found timepoint at index {} with timestamp <= {}", i, problematic_timestamp);
            } else {
                break;
            }
        }
        
        if let Some(index) = current_index {
            println!("  Current index: {}", index);
            if index > 0 {
                let prev_index = index - 1;
                let prev_timepoint = &timepoints[prev_index];
                println!("  Previous index: {}, timestamp: {}", prev_index, prev_timepoint.timestamp);
                
                // This is where the bug occurs - both timepoints have the same timestamp!
                if prev_timepoint.timestamp == problematic_timestamp {
                    println!("  BUG: Previous timepoint has the same timestamp!");
                }
            } else {
                println!("  Already at first timepoint");
            }
        }
    }
}
