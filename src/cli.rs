use clap::{Parse<PERSON>, ValueEnum};
use std::path::PathBuf;

use crate::logging::LogFormat;

// Define an enum for the data source options
#[derive(Debug, Clone, ValueEnum, PartialEq, Eq)]
pub enum DataSource {
    Baselink,
    Http,
}

#[derive(Parser, Debug)]
#[clap(name = "vehicle-webdisplay-server", version = "0.1.0", about = "WebSocket server for vehicle display", long_about = None)]
pub struct Cli {
    /// Specifies the source of the onboard data
    #[clap(long, value_enum, env = "DATA_SOURCE", default_value_t = DataSource::Baselink)]
    pub data_source: DataSource,

    /// URL for the baselink service (required if data_source is 'baselink')
    #[clap(long, env = "BASELINK_URL", default_value = "http://wowa-g3.local:50051")]
    pub baselink_url: String,

    /// Path to the JSON file containing OnboardData (kept for backward compatibility but not used)
    #[clap(long, env = "FILE_PATH", default_value = "trip.json")]
    pub file_path: PathBuf, // Use PathBuf for file paths

    /// Address to bind the WebSocket server to
    #[clap(long, env = "LISTEN_ADDR", default_value = "127.0.0.1:8765")]
    pub addr: String,

    /// The ID of the vehicle this service is running for
    #[clap(long, env = "VEHICLE_ID")]
    pub vehicle_id: String,

    /// Interval in seconds for fetching data from the source
    #[clap(long, env = "FETCH_INTERVAL_SECONDS", default_value = "15")]
    pub fetch_interval_seconds: u64,

    #[clap(long, env = "LOG_FORMAT")]
    pub log_format: Option<LogFormat>,

    /// Run data coordinator demonstration instead of normal operation
    #[clap(long, action)]
    pub demo: bool,
}
