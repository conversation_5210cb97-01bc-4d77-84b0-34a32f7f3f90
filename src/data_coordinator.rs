use tokio::sync::mpsc;
use tracing::{debug, info, warn, error, instrument};
use anyhow::Result;
use jiff::Timestamp;

use crate::onboard_model::{Journey, OnboardData, OnboardDataMessage, VehicleInformation, VehicleJourney};

/// Central data coordinator that manages onboard data state and deduplication
pub struct DataCoordinator {
    current_vehicle_journey: Option<VehicleJourney>,
    current_vehicle_info: Option<VehicleInformation>,
    output_tx: mpsc::UnboundedSender<OnboardData>,
}

impl DataCoordinator {
    /// Creates a new data coordinator
    pub fn new(output_tx: mpsc::UnboundedSender<OnboardData>) -> Self {
        Self {
            current_vehicle_journey: None,
            current_vehicle_info: None,
            output_tx,
        }
    }

    /// Processes incoming OnboardDataMessage and sends updates only when data changes
    #[instrument(skip(self), fields(data_type = ?std::mem::discriminant(&data)))]
    pub fn process_data(&mut self, data: OnboardDataMessage) -> Result<()> {
        let mut should_send = false;
        
        match data {
            OnboardDataMessage::VehicleJourney(vehicle_journey) => {
                debug!(trip_id = %vehicle_journey.journey.trip_id, "Received journey data");
                
                // Check if journey data has actually changed
                if self.current_vehicle_journey.as_ref() != Some(&vehicle_journey) {
                    self.current_vehicle_journey = Some(vehicle_journey);
                    should_send = true;
                }
            }
            OnboardDataMessage::VehicleInformation(vehicle_info) => {
                debug!(vehicle_id = %vehicle_info.vehicle_id, "Received vehicle information");
                
                // Check if vehicle info has actually changed
                if self.current_vehicle_info.as_ref() != Some(&vehicle_info) {
                    self.current_vehicle_info = Some(vehicle_info);
                    should_send = true;
                }
            }
            OnboardDataMessage::OffJourney => {
                info!("Vehicle went off journey");
                
                // Only send if we actually had a journey before
                if self.current_vehicle_journey.is_some() {
                    self.current_vehicle_journey = None;
                    should_send = true;
                }
            }
        }

        // Send data only if something changed and we have vehicle info
        if should_send {
            self.try_send_data()?;
        }
        
        Ok(())
    }

    /// Sends data if we have vehicle information
    fn try_send_data(&mut self) -> Result<()> {
        if let Some(onboard_data) = self.build_complete_data() {
            info!("Sending updated onboard data");
            
            if let Err(e) = self.output_tx.send(onboard_data) {
                error!(error = %e, "Failed to send onboard data");
                return Err(e.into());
            }
        } else {
            debug!("No vehicle information available, skipping send");
        }

        Ok(())
    }

    /// Builds onboard data if vehicle information is available
    fn build_complete_data(&self) -> Option<OnboardData> {
        let vehicle_information = self.current_vehicle_info.as_ref()?.clone();
        let vehicle_journey = self.current_vehicle_journey.clone();

        Some(OnboardData {
            current_time: Timestamp::now(),
            vehicle_information,
            vehicle_journey,
        })
    }

    /// Returns the current data state for debugging
    pub fn get_current_state(&self) -> (bool, bool) {
        (
            self.current_vehicle_info.is_some(),
            self.current_vehicle_journey.is_some(),
        )
    }
}

/// Runs the data coordinator task
#[instrument(skip(input_rx, output_tx))]
pub async fn run_data_coordinator(
    mut input_rx: mpsc::Receiver<OnboardDataMessage>,
    output_tx: mpsc::UnboundedSender<OnboardData>,
) -> Result<()> {
    info!("Starting data coordinator");
    
    let mut coordinator = DataCoordinator::new(output_tx);
    
    while let Some(data) = input_rx.recv().await {
        if let Err(e) = coordinator.process_data(data) {
            error!(error = %e, "Failed to process data in coordinator");
            // Continue processing despite errors
        }
    }
    
    warn!("Data coordinator input channel closed, shutting down");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::onboard_model::{DoorStatus, Call, ExitSide};

    fn create_test_vehicle_info() -> VehicleInformation {
        VehicleInformation {
            vehicle_id: "test-vehicle".to_string(),
            coach_number: Some("1".to_string()),
            door_status: DoorStatus::Closed,
        }
    }

    fn create_test_journey() -> Journey {
        Journey {
            trip_id: "test-trip".to_string(),
            destination_text: "Test Destination".to_string(),
            line_name: "Test Line".to_string(),
            calls: vec![
                Call {
                    stop_id: "stop1".to_string(),
                    seq_number: 1,
                    scheduled_arrival_time: Some(1000),
                    scheduled_departure_time: Some(1100),
                    actual_arrival_time: None,
                    actual_departure_time: None,
                    destination_text: Some("Test Destination".to_string()),
                    platform_name: Some("Platform 1".to_string()),
                    stop_name: "Stop 1".to_string(),
                    advisory_messages: vec![],
                    is_cancelled: false,
                    exit_side: ExitSide::Left,
                },
                Call {
                    stop_id: "stop2".to_string(),
                    seq_number: 2,
                    scheduled_arrival_time: Some(1200),
                    scheduled_departure_time: Some(1300),
                    actual_arrival_time: None,
                    actual_departure_time: None,
                    destination_text: Some("Test Destination".to_string()),
                    platform_name: Some("Platform 2".to_string()),
                    stop_name: "Stop 2".to_string(),
                    advisory_messages: vec![],
                    is_cancelled: false,
                    exit_side: ExitSide::Right,
                },
            ],
        }
    }

    #[tokio::test]
    async fn test_coordinator_basic_functionality() {
        let (output_tx, mut output_rx) = mpsc::unbounded_channel();
        let mut coordinator = DataCoordinator::new(output_tx);

        // Send vehicle info first
        coordinator.process_data(OnboardDataMessage::VehicleInformation(create_test_vehicle_info())).unwrap();
        
        // Should send data with vehicle info but no journey
        let received_data = output_rx.try_recv().unwrap();
        assert!(received_data.vehicle_journey.is_none());
        assert_eq!(received_data.vehicle_information.vehicle_id, "test-vehicle");

        // Send journey data
        coordinator.process_data(OnboardDataMessage::VehicleJourney(VehicleJourney::from_journey(create_test_journey()))).unwrap();
        
        // Should now send updated data with journey
        let received_data = output_rx.try_recv().unwrap();
        assert!(received_data.vehicle_journey.is_some());
        assert_eq!(received_data.vehicle_information.vehicle_id, "test-vehicle");
    }

    #[tokio::test]
    async fn test_coordinator_deduplication() {
        let (output_tx, mut output_rx) = mpsc::unbounded_channel();
        let mut coordinator = DataCoordinator::new(output_tx);

        // Set up initial state
        coordinator.process_data(OnboardDataMessage::VehicleInformation(create_test_vehicle_info())).unwrap();
        
        // Should send data with vehicle info but no journey (first message)
        let _received_data = output_rx.try_recv().unwrap();
        
        coordinator.process_data(OnboardDataMessage::VehicleJourney(VehicleJourney::from_journey(create_test_journey()))).unwrap();
        
        // Should send data with both vehicle info and journey (second message)
        let _received_data = output_rx.try_recv().unwrap();

        // Send the same data again
        coordinator.process_data(OnboardDataMessage::VehicleInformation(create_test_vehicle_info())).unwrap();
        coordinator.process_data(OnboardDataMessage::VehicleJourney(VehicleJourney::from_journey(create_test_journey()))).unwrap();
        
        // Should not send duplicate data
        assert!(output_rx.try_recv().is_err());
    }

    #[tokio::test]
    async fn test_coordinator_off_journey() {
        let (output_tx, mut output_rx) = mpsc::unbounded_channel();
        let mut coordinator = DataCoordinator::new(output_tx);

        // Set up initial state with both journey and vehicle info
        coordinator.process_data(OnboardDataMessage::VehicleInformation(create_test_vehicle_info())).unwrap();
        
        // Should send data with vehicle info but no journey (first message)
        let _received_data = output_rx.try_recv().unwrap();
        
        coordinator.process_data(OnboardDataMessage::VehicleJourney(VehicleJourney::from_journey(create_test_journey()))).unwrap();
        
        // Should send data with both vehicle info and journey (second message)
        let _received_data = output_rx.try_recv().unwrap();

        // Go off journey
        coordinator.process_data(OnboardDataMessage::OffJourney).unwrap();
        
        // Should send data with no journey
        let received_data = output_rx.try_recv().unwrap();
        assert!(received_data.vehicle_journey.is_none());
        assert_eq!(received_data.vehicle_information.vehicle_id, "test-vehicle");
    }
} 