use std::io::IsTerminal;

use tracing_subscriber::{fmt, layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};

/// Sets up the application logging system using the `tracing` crate.
///
/// This function configures `tracing` with different output formats based on the execution environment:
/// - **Terminal:** If the program is run in a terminal (stdout is a TTY) and the `LOG_FORMAT` environment
///   variable is not set to `json-line`, it uses a human-readable, pretty-printed format with colors.
/// - **Non-Terminal/JSON:** Otherwise (e.g., running in a container, CI, or if `LOG_FORMAT=json-line`),
///   it uses a JSON Lines format suitable for machine parsing (like in staging/production environments).
///   Event attributes are flattened into the main JSON object.
///
/// The log level filtering is determined by the `RUST_LOG` environment variable. If `RUST_LOG` is not set,
/// it defaults to `info`.
pub(crate) fn setup_logging(log_format: &Option<LogFormat>) {
    let filter = EnvFilter::try_from_default_env().unwrap_or_else(|_| EnvFilter::new("info"));

    if std::io::stdout().is_terminal()
        && log_format.map(|f| f != LogFormat::JsonLine).unwrap_or(true)
    {
        // use pretty formatter for human-readable output when in a terminal
        let fmt_layer = fmt::layer()
            .pretty()
            .with_timer(tracing_subscriber::fmt::time::SystemTime)
            .with_target(true);
        tracing_subscriber::registry()
            .with(filter)
            .with(fmt_layer)
            .init();
        // Return early as the subscriber is initialized.
        return;
    }
    // use json-lines format for machine parsing when not in a terminal
    let fmt_layer = fmt::layer()
        .json()
        .flatten_event(true) // flatten the event attributes into a single JSON object for stg0
        .with_span_list(false)
        .with_current_span(true)
        .with_target(false);
    tracing_subscriber::registry()
        .with(filter)
        .with(fmt_layer)
        .init();
}

#[derive(Debug, Clone, Copy, Eq, PartialEq, clap::ValueEnum)]
pub enum LogFormat {
    // clap interpret it as "pretty"
    #[clap(name = "pretty")]
    Pretty,
    #[clap(name = "json-line")]
    JsonLine,
}
