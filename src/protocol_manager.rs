use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc::{UnboundedSender, UnboundedR<PERSON>eiver};
use tokio::time::interval;
use tracing::{debug, info, warn, error, instrument};
use anyhow::Result;

use crate::onboard_display_protocol::{OnboardDisplayProtocol, ConnectionId};
use crate::onboard_protocol::{ProtocolMessage, ClientMessage};
use crate::device_identification::{DeviceIdentificationService, ConnectionMetadata};
use crate::onboard_model::OnboardData;

/// Manages multiple protocol instances and coordinates data broadcasting
pub struct ProtocolManager {
    /// Device identification service
    identification_service: Arc<DeviceIdentificationService>,
    /// Active protocol instances
    protocols: HashMap<ConnectionId, OnboardDisplayProtocol>,
    /// Connection counter for unique IDs
    connection_counter: u64,
    /// Channel for receiving onboard data updates
    data_rx: UnboundedReceiver<OnboardData>,
    /// Timeout check interval
    timeout_check_interval: Duration,
}

/// Message from WebSocket connection to protocol manager
#[derive(Debug)]
pub enum ConnectionMessage {
    /// New connection established
    NewConnection {
        connection_id: ConnectionId,
        addr: SocketAddr,
        message_tx: UnboundedSender<ProtocolMessage>,
    },
    /// Client message received
    ClientMessage {
        connection_id: ConnectionId,
        message: ClientMessage,
    },
    /// Connection closed
    ConnectionClosed {
        connection_id: ConnectionId,
    },
}

impl ProtocolManager {
    /// Create a new protocol manager
    pub fn new(
        identification_service: Arc<DeviceIdentificationService>,
        data_rx: UnboundedReceiver<OnboardData>,
    ) -> Self {
        Self {
            identification_service,
            protocols: HashMap::new(),
            connection_counter: 0,
            data_rx,
            timeout_check_interval: Duration::from_millis(100), // Check timeouts every 100ms
        }
    }

    /// Run the protocol manager main loop
    #[instrument(skip(self))]
    pub async fn run(mut self, mut connection_rx: UnboundedReceiver<ConnectionMessage>) -> Result<()> {
        info!("Starting protocol manager");
        
        let mut timeout_interval = interval(self.timeout_check_interval);
        
        loop {
            tokio::select! {
                // Handle connection messages
                Some(conn_msg) = connection_rx.recv() => {
                    if let Err(e) = self.handle_connection_message(conn_msg).await {
                        error!(error = %e, "Failed to handle connection message");
                    }
                }
                
                // Handle onboard data updates
                Some(data) = self.data_rx.recv() => {
                    if let Err(e) = self.broadcast_onboard_data(data).await {
                        error!(error = %e, "Failed to broadcast onboard data");
                    }
                }
                
                // Check for timeouts
                _ = timeout_interval.tick() => {
                    if let Err(e) = self.check_timeouts().await {
                        error!(error = %e, "Failed to check timeouts");
                    }
                }
                
                // Handle shutdown
                else => {
                    info!("Protocol manager shutting down");
                    break;
                }
            }
        }
        
        Ok(())
    }

    /// Handle incoming connection messages
    async fn handle_connection_message(&mut self, message: ConnectionMessage) -> Result<()> {
        match message {
            ConnectionMessage::NewConnection { connection_id, addr, message_tx } => {
                self.handle_new_connection(connection_id, addr, message_tx).await
            }
            ConnectionMessage::ClientMessage { connection_id, message } => {
                self.handle_client_message(connection_id, message).await
            }
            ConnectionMessage::ConnectionClosed { connection_id } => {
                self.handle_connection_closed(connection_id).await
            }
        }
    }

    /// Handle new WebSocket connection
    async fn handle_new_connection(
        &mut self,
        connection_id: ConnectionId,
        addr: SocketAddr,
        message_tx: UnboundedSender<ProtocolMessage>,
    ) -> Result<()> {
        info!(
            connection_id = connection_id,
            addr = %addr,
            "New WebSocket connection"
        );

        // Create connection metadata from socket address
        let connection_metadata = ConnectionMetadata {
            ip: addr.ip(),
            display_id: None, // Will be set if Hello message is received
            dns: None, // DNS resolution not implemented yet
        };

        // Create new protocol instance
        let protocol = OnboardDisplayProtocol::new(
            connection_id,
            connection_metadata,
            message_tx,
        );

        // Store the protocol instance
        self.protocols.insert(connection_id, protocol);

        info!(
            connection_id = connection_id,
            total_connections = self.protocols.len(),
            "Protocol instance created"
        );

        Ok(())
    }

    /// Handle client message
    async fn handle_client_message(
        &mut self,
        connection_id: ConnectionId,
        message: ClientMessage,
    ) -> Result<()> {
        debug!(
            connection_id = connection_id,
            message = ?message,
            "Received client message"
        );

        if let Some(protocol) = self.protocols.get_mut(&connection_id) {
            match message {
                ClientMessage::Hello { .. } => {
                    protocol.handle_hello_message(message, self.identification_service.clone()).await?;
                }
                ClientMessage::Bye { .. } => {
                    protocol.handle_bye_message(message).await?;
                }
            }
        } else {
            warn!(
                connection_id = connection_id,
                "Received message for unknown connection"
            );
        }

        Ok(())
    }

    /// Handle connection closed
    async fn handle_connection_closed(&mut self, connection_id: ConnectionId) -> Result<()> {
        if let Some(_protocol) = self.protocols.remove(&connection_id) {
            info!(
                connection_id = connection_id,
                remaining_connections = self.protocols.len(),
                "Connection closed and protocol instance removed"
            );
        } else {
            warn!(
                connection_id = connection_id,
                "Attempted to close unknown connection"
            );
        }

        Ok(())
    }

    /// Check for Hello timeouts and handle them
    async fn check_timeouts(&mut self) -> Result<()> {
        let mut timeouts_to_handle = Vec::new();

        // Collect connections that have timed out
        for (connection_id, protocol) in &self.protocols {
            if protocol.is_hello_timeout_expired() {
                timeouts_to_handle.push(*connection_id);
            }
        }

        // Handle timeouts
        for connection_id in timeouts_to_handle {
            if let Some(protocol) = self.protocols.get_mut(&connection_id) {
                debug!(
                    connection_id = connection_id,
                    "Handling Hello timeout"
                );
                
                if let Err(e) = protocol.handle_hello_timeout(self.identification_service.clone()).await {
                    warn!(
                        connection_id = connection_id,
                        error = %e,
                        "Failed to handle Hello timeout"
                    );
                }
            }
        }

        Ok(())
    }

    /// Broadcast onboard data to all connected clients in streaming state
    async fn broadcast_onboard_data(&mut self, data: OnboardData) -> Result<()> {
        debug!("Broadcasting onboard data to {} connections", self.protocols.len());

        let mut failed_connections = Vec::new();

        for (connection_id, protocol) in &mut self.protocols {
            if let Err(e) = protocol.send_onboard_data(data.clone()).await {
                warn!(
                    connection_id = connection_id,
                    error = %e,
                    "Failed to send onboard data to connection"
                );
                
                // Mark connection for removal if it's terminated
                if protocol.is_terminated() {
                    failed_connections.push(*connection_id);
                }
            }
        }

        // Remove failed connections
        for connection_id in failed_connections {
            self.protocols.remove(&connection_id);
            info!(
                connection_id = connection_id,
                "Removed terminated connection"
            );
        }

        Ok(())
    }

    /// Generate next connection ID
    pub fn next_connection_id(&mut self) -> ConnectionId {
        self.connection_counter += 1;
        self.connection_counter
    }

    /// Get current number of active connections
    pub fn connection_count(&self) -> usize {
        self.protocols.len()
    }

    /// Get connection statistics for debugging
    pub fn get_connection_stats(&self) -> HashMap<ConnectionId, String> {
        self.protocols
            .iter()
            .map(|(id, protocol)| (*id, protocol.current_state().to_string()))
            .collect()
    }
}
