warning: unused import: `Journey`
 --> src/data_coordinator.rs:5:28
  |
5 | use crate::onboard_model::{Journey, OnboardData, OnboardDataMessage, VehicleInformation, VehicleJourney};
  |                            ^^^^^^^
  |
  = note: `#[warn(unused_imports)]` on by default

warning: unused import: `VehicleJourney`
 --> src/http_data_fetcher.rs:6:68
  |
6 | use crate::onboard_model::{OnboardDataMessage, VehicleInformation, VehicleJourney, DoorStatus};
  |                                                                    ^^^^^^^^^^^^^^

warning: associated items `current_status`, `current_call_index`, and `from_journey` are never used
   --> src/onboard_model.rs:95:12
    |
94  | impl VehicleJourney {
    | ------------------- associated items in this implementation
95  |     pub fn current_status(&self) -> Option<LocationStatus> {
    |            ^^^^^^^^^^^^^^
...
98  |     pub fn current_call_index(&self) -> Option<usize> {
    |            ^^^^^^^^^^^^^^^^^^
...
102 |     pub fn from_journey(journey: Journey) -> Self {
    |            ^^^^^^^^^^^^
    |
    = note: `#[warn(dead_code)]` on by default

warning: methods `next` and `previous` are never used
   --> src/onboard_model.rs:119:12
    |
118 | impl LocationStatus {
    | ------------------- methods in this implementation
119 |     pub fn next(&self) -> Self {
    |            ^^^^
...
128 |     pub fn previous(&self) -> Self {
    |            ^^^^^^^^

warning: methods `get_vehicle_trip` and `get_active_trips` are never used
  --> src/baselink_api_client.rs:52:18
   |
20 | impl BaselinkApi {
   | ---------------- methods in this implementation
...
52 |     pub async fn get_vehicle_trip(&self, vehicle_id: &str) -> Result<Option<Trip>> {
   |                  ^^^^^^^^^^^^^^^^
...
72 |     pub async fn get_active_trips(&self, line_name: Option<&str>) -> Result<Vec<Trip>> {
   |                  ^^^^^^^^^^^^^^^^

warning: method `current_timepoint` is never used
  --> src/journey_simulation.rs:82:12
   |
26 | impl JourneySimulation {
   | ---------------------- method in this implementation
...
82 |     pub fn current_timepoint(&self) -> &TimePoint {
   |            ^^^^^^^^^^^^^^^^^

warning: methods `arrival_in_past` and `departure_in_past` are never used
  --> src/journey_timeline.rs:61:12
   |
41 | impl Call {
   | --------- methods in this implementation
...
61 |     pub fn arrival_in_past(&self, now: i64) -> bool {
   |            ^^^^^^^^^^^^^^^
...
67 |     pub fn departure_in_past(&self, now: i64) -> bool {
   |            ^^^^^^^^^^^^^^^^^

warning: methods `find_next_state`, `find_previous_state`, and `timepoints` are never used
   --> src/journey_timeline.rs:99:12
    |
79  | impl JourneyTimeline {
    | -------------------- methods in this implementation
...
99  |     pub fn find_next_state(&self, timestamp: i64) -> Option<TimePoint> {
    |            ^^^^^^^^^^^^^^^
...
114 |     pub fn find_previous_state(&self, timestamp: i64) -> Option<TimePoint> {
    |            ^^^^^^^^^^^^^^^^^^^
...
256 |     pub fn timepoints(&self) -> &[TimePoint] {
    |            ^^^^^^^^^^

warning: method `get_current_state` is never used
  --> src/data_coordinator.rs:95:12
   |
14 | impl DataCoordinator {
   | -------------------- method in this implementation
...
95 |     pub fn get_current_state(&self) -> (bool, bool) {
   |            ^^^^^^^^^^^^^^^^^

warning: associated functions `error` and `error_with_type` are never used
  --> src/http_api/types.rs:35:12
   |
24 | impl<T> ApiResponse<T> {
   | ---------------------- associated functions in this implementation
...
35 |     pub fn error(code: &str, message: &str, details: Option<String>) -> ApiResponse<()> {
   |            ^^^^^
...
49 |     pub fn error_with_type<U>(code: &str, message: &str, details: Option<String>) -> ApiResponse<U> {
   |            ^^^^^^^^^^^^^^^

warning: method `has_journey` is never used
   --> src/simulation_manager.rs:287:12
    |
26  | impl SimulationManager {
    | ---------------------- method in this implementation
...
287 |     pub fn has_journey(&self) -> bool {
    |            ^^^^^^^^^^^

warning: `onboard-data-server` (bin "onboard-data-server") generated 11 warnings (run `cargo fix --bin "onboard-data-server"` to apply 2 suggestions)
    Finished `dev` profile [unoptimized + debuginfo] target(s) in 1.21s
warning: the following packages contain code that will be rejected by a future version of Rust: buf_redux v0.8.4, multipart v0.18.0, typemap v0.3.3
note: to see what the problems were, use the option `--future-incompat-report`, or run `cargo report future-incompatibilities --id 2`
     Running `target/debug/onboard-data-server --data-source http --vehicle-id test-vehicle`
{"timestamp":"2025-06-22T07:52:05.382851Z","level":"INFO","message":"Starting server with CLI arguments: Cli { data_source: Http, baselink_url: \"http://wowa-g3.local:50051\", file_path: \"trip.json\", addr: \"127.0.0.1:8765\", vehicle_id: \"test-vehicle\", fetch_interval_seconds: 15, log_format: None, demo: false }"}
{"timestamp":"2025-06-22T07:52:05.383317Z","level":"INFO","message":"Data coordinator task spawned."}
{"timestamp":"2025-06-22T07:52:05.383326Z","level":"INFO","message":"Initializing Baselink API connection to: http://wowa-g3.local:50051"}
{"timestamp":"2025-06-22T07:52:05.383694Z","level":"INFO","message":"Starting data coordinator","span":{"name":"run_data_coordinator"}}
{"timestamp":"2025-06-22T07:52:05.905608Z","level":"INFO","message":"Connected to Baselink Server version: Version { major: 0, minor: 39, patch: 0 }"}
{"timestamp":"2025-06-22T07:52:05.909974Z","level":"INFO","message":"Vehicles: [Vehicle { id: \"Vehicle 1 (Line 7)\", operating_day: 1750543200, block_id: \"\", trip_id: \"427160367\", course_id: \"\", line_id: \"14152\", gps_position: None }, Vehicle { id: \"Vehicle 3 (Line 13)\", operating_day: 1750543200, block_id: \"\", trip_id: \"427165458\", course_id: \"\", line_id: \"75363\", gps_position: None }, Vehicle { id: \"Vehicle 2 (Line 3)\", operating_day: 1750543200, block_id: \"\", trip_id: \"427150367\", course_id: \"\", line_id: \"14148\", gps_position: None }]"}
{"timestamp":"2025-06-22T07:52:05.910563Z","level":"INFO","message":"HTTP API server configured with simulation, data, and baselink endpoints"}
{"timestamp":"2025-06-22T07:52:05.910586Z","level":"INFO","message":"Using HTTP API data source with simulation data."}
{"timestamp":"2025-06-22T07:52:05.910602Z","level":"INFO","message":"HTTP data fetcher task spawned."}
{"timestamp":"2025-06-22T07:52:05.910610Z","level":"INFO","message":"Starting HTTP API server","address":"127.0.0.1:8080"}
{"timestamp":"2025-06-22T07:52:05.910621Z","level":"INFO","message":"HTTP API server started on 127.0.0.1:8080"}
{"timestamp":"2025-06-22T07:52:05.910629Z","level":"INFO","message":"Starting WebSocket server","address":"127.0.0.1:8765"}
{"timestamp":"2025-06-22T07:52:05.910684Z","level":"INFO","message":"Starting HTTP data fetcher","vehicle_id":"test-vehicle","interval_seconds":15,"span":{"name":"run_http_data_fetcher"}}
{"timestamp":"2025-06-22T07:52:05.910743Z","level":"INFO","message":"WebSocket server listening","address":"127.0.0.1:8765","span":{"addr":"127.0.0.1:8765","name":"start_server"}}
{"timestamp":"2025-06-22T07:52:05.910786Z","level":"INFO","message":"Sending updated onboard data","span":{"data":"VehicleInformation(VehicleInformation { vehicle_id: \"test-vehicle\", coach_number: Some(\"1\"), door_status: Closed })","data_type":"Discriminant(2)","name":"process_data"}}
{"timestamp":"2025-06-22T07:52:05.910804Z","level":"INFO","message":"Starting data processing task (receive, store last, broadcast)."}
{"timestamp":"2025-06-22T07:52:05.910822Z","level":"INFO","message":"Received complete OnboardData update via channel."}
{"timestamp":"2025-06-22T07:52:20.916235Z","level":"INFO","message":"Loading journey: 427149967"}
{"timestamp":"2025-06-22T07:52:20.916365Z","level":"INFO","message":"Journey loaded: 427149967"}
{"timestamp":"2025-06-22T07:52:20.925933Z","level":"INFO","message":"Playback started with speed: 4"}
DEBUG: tick() called - is_playing: true
DEBUG: tick() - now: 1750578755, rtc_timestamp: 1750578740, diff: 15, speed_factor: 4, old_timestamp: 1750510566, new_timestamp: 1750510626
DEBUG: tick() - after set_time: current_timestamp: 1750510566
{"timestamp":"2025-06-22T07:52:35.911942Z","level":"INFO","message":"Journey changed, sending update","old_journey":"None","new_journey":"427149967","span":{"name":"run_http_data_fetcher"}}
{"timestamp":"2025-06-22T07:52:35.912318Z","level":"INFO","message":"Sending updated onboard data","span":{"data":"VehicleJourney(VehicleJourney { journey: Journey { trip_id: \"427149967\", destination_text: \"Altwarmbüchen\", line_name: \"3\", calls: [Call { stop_id: \"de:03241:5181:1:5182\", seq_number: 0, scheduled_arrival_time: Some(1750508040), scheduled_departure_time: Some(1750508040), actual_arrival_time: None, actual_departure_time: Some(1750508048), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Wettbergen\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:5177:4:5178\", seq_number: 1, scheduled_arrival_time: Some(1750508100), scheduled_departure_time: Some(1750508100), actual_arrival_time: Some(1750508100), actual_departure_time: Some(1750508124), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Tresckowstraße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:1001:7:1002\", seq_number: 2, scheduled_arrival_time: Some(1750508172), scheduled_departure_time: Some(1750508172), actual_arrival_time: Some(1750508187), actual_departure_time: Some(1750508216), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Mühlenberger Markt\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:991:2:992\", seq_number: 3, scheduled_arrival_time: Some(1750508250), scheduled_departure_time: Some(1750508250), actual_arrival_time: Some(1750508299), actual_departure_time: Some(1750508323), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Am Sauerwinkel\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:981:3:982\", seq_number: 4, scheduled_arrival_time: Some(1750508328), scheduled_departure_time: Some(1750508328), actual_arrival_time: Some(1750508373), actual_departure_time: Some(1750508373), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Bartold-Knaust-Straße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:971:5:972\", seq_number: 5, scheduled_arrival_time: Some(1750508424), scheduled_departure_time: Some(1750508424), actual_arrival_time: Some(1750508442), actual_departure_time: Some(1750508460), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Wallensteinstraße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:961:1:962\", seq_number: 6, scheduled_arrival_time: Some(1750508514), scheduled_departure_time: Some(1750508514), actual_arrival_time: Some(1750508522), actual_departure_time: Some(1750508545), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Beekestraße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:951:1:952\", seq_number: 7, scheduled_arrival_time: Some(1750508586), scheduled_departure_time: Some(1750508586), actual_arrival_time: Some(1750508590), actual_departure_time: Some(1750508617), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Schünemannplatz\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:841:2:844\", seq_number: 8, scheduled_arrival_time: Some(1750508670), scheduled_departure_time: Some(1750508670), actual_arrival_time: Some(1750508670), actual_departure_time: Some(1750508696), destination_text: Some(\"\"), platform_name: Some(\"SB 2\"), stop_name: \"Bahnhof Linden/Fischerhof\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:881:1:882\", seq_number: 9, scheduled_arrival_time: Some(1750508754), scheduled_departure_time: Some(1750508754), actual_arrival_time: Some(1750508754), actual_departure_time: Some(1750508770), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Stadionbrücke\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:871:7:872\", seq_number: 10, scheduled_arrival_time: Some(1750508838), scheduled_departure_time: Some(1750508838), actual_arrival_time: Some(1750508838), actual_departure_time: Some(1750508870), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Allerweg\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:451:4:454\", seq_number: 11, scheduled_arrival_time: Some(1750508940), scheduled_departure_time: Some(1750508940), actual_arrival_time: Some(1750508950), actual_departure_time: Some(1750508967), destination_text: Some(\"\"), platform_name: Some(\"SB4\"), stop_name: \"Waterloo\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:91:4:92\", seq_number: 12, scheduled_arrival_time: Some(1750509000), scheduled_departure_time: Some(1750509000), actual_arrival_time: Some(1750509017), actual_departure_time: Some(1750509040), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Markthalle/Landtag\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:11:1:12\", seq_number: 13, scheduled_arrival_time: Some(1750509060), scheduled_departure_time: Some(1750509060), actual_arrival_time: Some(1750509080), actual_departure_time: Some(1750509126), destination_text: Some(\"\"), platform_name: Some(\"#2/SB\"), stop_name: \"Kröpcke\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:31:4:22\", seq_number: 14, scheduled_arrival_time: Some(1750509180), scheduled_departure_time: Some(1750509180), actual_arrival_time: Some(1750509200), actual_departure_time: Some(1750509246), destination_text: Some(\"\"), platform_name: Some(\"UB2\"), stop_name: \"Hauptbahnhof\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2001:3:2002\", seq_number: 15, scheduled_arrival_time: Some(1750509258), scheduled_departure_time: Some(1750509258), actual_arrival_time: Some(1750509278), actual_departure_time: Some(1750509324), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Sedanstraße/Lister M.\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2021:2:2022\", seq_number: 16, scheduled_arrival_time: Some(1750509342), scheduled_departure_time: Some(1750509342), actual_arrival_time: Some(1750509362), actual_departure_time: Some(1750509408), destination_text: Some(\"\"), platform_name: Some(\"UG 1\"), stop_name: \"Lister Platz\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2121:6:2122\", seq_number: 17, scheduled_arrival_time: Some(1750509414), scheduled_departure_time: Some(1750509414), actual_arrival_time: Some(1750509434), actual_departure_time: Some(1750509480), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Lortzingstraße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2131:7:2132\", seq_number: 18, scheduled_arrival_time: Some(1750509486), scheduled_departure_time: Some(1750509486), actual_arrival_time: Some(1750509506), actual_departure_time: Some(1750509552), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Vier Grenzen\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2141:1:2142\", seq_number: 19, scheduled_arrival_time: Some(1750509570), scheduled_departure_time: Some(1750509570), actual_arrival_time: Some(1750509590), actual_departure_time: Some(1750509636), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Pelikanstraße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2151:4:2152\", seq_number: 20, scheduled_arrival_time: Some(1750509654), scheduled_departure_time: Some(1750509654), actual_arrival_time: Some(1750509674), actual_departure_time: Some(1750509720), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Spannhagengarten\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2161:3:2162\", seq_number: 21, scheduled_arrival_time: Some(1750509732), scheduled_departure_time: Some(1750509732), actual_arrival_time: Some(1750509752), actual_departure_time: Some(1750509798), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Klingerstraße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2171:5:2172\", seq_number: 22, scheduled_arrival_time: Some(1750509840), scheduled_departure_time: Some(1750509840), actual_arrival_time: Some(1750509860), actual_departure_time: Some(1750509906), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Noltemeyerbrücke\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2231:2:2232\", seq_number: 23, scheduled_arrival_time: Some(1750509924), scheduled_departure_time: Some(1750509924), actual_arrival_time: Some(1750509944), actual_departure_time: Some(1750509990), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"In den Sieben Stücken\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2301:9:2302\", seq_number: 24, scheduled_arrival_time: Some(1750510008), scheduled_departure_time: Some(1750510008), actual_arrival_time: Some(1750510028), actual_departure_time: Some(1750510074), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Paracelsusweg\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2321:4:2322\", seq_number: 25, scheduled_arrival_time: Some(1750510110), scheduled_departure_time: Some(1750510110), actual_arrival_time: Some(1750510130), actual_departure_time: Some(1750510176), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Stadtfriedhof Lahe\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:2325:4:2326\", seq_number: 26, scheduled_arrival_time: Some(1750510176), scheduled_departure_time: Some(1750510176), actual_arrival_time: Some(1750510196), actual_departure_time: Some(1750510242), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Oldenburger Allee\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:6683:4:6684\", seq_number: 27, scheduled_arrival_time: Some(1750510290), scheduled_departure_time: Some(1750510290), actual_arrival_time: Some(1750510310), actual_departure_time: Some(1750510356), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Altwarmbüchen Opelstraße\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:6689:4:6690\", seq_number: 28, scheduled_arrival_time: Some(1750510362), scheduled_departure_time: Some(1750510362), actual_arrival_time: Some(1750510382), actual_departure_time: Some(1750510428), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Altwarmbüchen Ernst-Grote-Str.\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:6637:5:6638\", seq_number: 29, scheduled_arrival_time: Some(1750510440), scheduled_departure_time: Some(1750510440), actual_arrival_time: Some(1750510460), actual_departure_time: Some(1750510506), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Altwarmbüchen Zentrum\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }, Call { stop_id: \"de:03241:6641:5:6642\", seq_number: 30, scheduled_arrival_time: Some(1750510500), scheduled_departure_time: Some(1750510500), actual_arrival_time: Some(1750510520), actual_departure_time: Some(1750510566), destination_text: Some(\"\"), platform_name: Some(\"SB2\"), stop_name: \"Altwarmbüchen\", advisory_messages: [], is_cancelled: false, exit_side: Unknown }] }, current_call_status: Some(CurrentCallStatus { call_index: 30, status: AfterStop }) })","data_type":"Discriminant(0)","name":"process_data"}}
{"timestamp":"2025-06-22T07:52:35.912687Z","level":"INFO","message":"Received complete OnboardData update via channel."}
DEBUG: tick() called - is_playing: true
DEBUG: tick() - now: 1750578770, rtc_timestamp: 1750578755, diff: 15, speed_factor: 4, old_timestamp: 1750510566, new_timestamp: 1750510626
DEBUG: tick() - after set_time: current_timestamp: 1750510566
DEBUG: tick() called - is_playing: true
DEBUG: tick() - now: 1750578785, rtc_timestamp: 1750578770, diff: 15, speed_factor: 4, old_timestamp: 1750510566, new_timestamp: 1750510626
DEBUG: tick() - after set_time: current_timestamp: 1750510566
DEBUG: tick() called - is_playing: true
DEBUG: tick() - now: 1750578800, rtc_timestamp: 1750578785, diff: 15, speed_factor: 4, old_timestamp: 1750510566, new_timestamp: 1750510626
DEBUG: tick() - after set_time: current_timestamp: 1750510566
DEBUG: tick() called - is_playing: true
DEBUG: tick() - now: 1750578815, rtc_timestamp: 1750578800, diff: 15, speed_factor: 4, old_timestamp: 1750510566, new_timestamp: 1750510626
DEBUG: tick() - after set_time: current_timestamp: 1750510566
DEBUG: tick() called - is_playing: true
DEBUG: tick() - now: 1750578830, rtc_timestamp: 1750578815, diff: 15, speed_factor: 4, old_timestamp: 1750510566, new_timestamp: 1750510626
DEBUG: tick() - after set_time: current_timestamp: 1750510566
DEBUG: tick() called - is_playing: true
DEBUG: tick() - now: 1750578845, rtc_timestamp: 1750578830, diff: 15, speed_factor: 4, old_timestamp: 1750510566, new_timestamp: 1750510626
DEBUG: tick() - after set_time: current_timestamp: 1750510566
