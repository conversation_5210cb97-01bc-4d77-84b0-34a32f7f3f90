# Device Identification Service

The Device Identification Service is a core component of ODIS that manages device identification and context assignment for connected display devices.

## Overview

When display devices connect to ODIS via WebSocket, they need to be identified and provided with appropriate configuration metadata. The Device Identification Service handles this process using configurable rules and provides device-specific context information.

## Key Responsibilities

### Device Identification
- **Rule-based Matching**: Uses configurable rules to identify connecting devices
- **Context Assignment**: Provides device-specific metadata (type, location, configuration)
- **Dynamic Updates**: Supports runtime configuration changes without requiring reconnection

### Configuration Management
- **Static Configuration**: JSON-based device identification rules loaded from file
- **Runtime Reload**: Configuration can be reloaded via HTTP API without service restart
- **Validation**: Comprehensive validation of configuration files before application

## Device Identification Process

1. **Device Connection**: Display device connects to ODIS WebSocket endpoint
2. **Rule Evaluation**: Service evaluates device properties against configured rules
3. **Context Assignment**: Matching rule provides device context (type, location, layout)
4. **Metadata Response**: Device receives identification metadata via WebSocket
5. **Ongoing Service**: Devi<PERSON> uses context to determine appropriate behavior

## Configuration Format

Device identification rules are stored in JSON format:

```json
[
    {
        "lookup": {
            "ip": "*************",
            "dns": "door-display-left-1",
            "displayId": "display_001"
        },
        "deviceId": "door-left-display",
        "deviceClassifier": "onboard-display-information"
    }
]
```

### Configuration Fields

- **lookup**: Criteria for matching connecting devices
  - **ip**: IP address matching
  - **dns**: DNS name matching
  - **displayId**: Explicit device ID matching
- **deviceId**: Unique identifier assigned to matched device
- **deviceClassifier**: Device type classification

## Runtime Configuration Management

### HTTP API Endpoints

- `POST /admin/reload-device-config` - Reload configuration from file
- `GET /admin/device-config` - Get current configuration (planned)

### Configuration Reload Process

1. **API Request**: Administrator triggers configuration reload
2. **File Loading**: Service loads configuration file from disk
3. **Validation**: Comprehensive validation of new configuration
4. **Atomic Update**: If valid, configuration is atomically replaced
5. **Re-identification**: Active connections are re-identified with new rules
6. **Response**: API returns success/failure status

## Integration with ODIS

### WebSocket Protocol Integration

The Device Identification Service integrates seamlessly with the ODIS WebSocket protocol:

- **Connection State**: Manages device identification during connection establishment
- **Message Flow**: Sends device-identification messages to newly connected devices
- **State Management**: Maintains device context throughout connection lifecycle

### Data Distribution

Once devices are identified, the service coordinates with the Data Coordinator to ensure:

- **Targeted Data**: Devices receive data appropriate for their context
- **Layout Assignment**: Devices know which display layout to use
- **Configuration Updates**: Dynamic configuration changes are propagated

## Error Handling

### Configuration Errors
- **File Not Found**: Graceful handling when configuration file is missing
- **Invalid JSON**: Detailed error messages for syntax errors
- **Schema Validation**: Validation of required fields and data types
- **Logical Validation**: Checking for duplicate entries and conflicts

### Runtime Errors
- **Identification Failures**: Fallback behavior when device cannot be identified
- **Network Issues**: Handling of connection problems during identification
- **Configuration Reload Failures**: Preserving existing configuration on reload errors

## Future Enhancements

### Advanced Matching Rules
- **Pattern Matching**: Support for regex patterns in device identification
- **Conditional Rules**: Time-based or context-based rule evaluation
- **Rule Priorities**: Ordered rule evaluation with priority handling

### Enhanced Management
- **Configuration Versioning**: Track configuration changes over time
- **Rollback Capabilities**: Ability to revert to previous configurations
- **Audit Logging**: Comprehensive logging of configuration changes

### Integration Features
- **External Configuration**: Integration with external configuration management systems
- **Service Discovery**: Automatic device discovery and registration
- **Multi-Environment**: Support for different configurations per environment

---

The Device Identification Service provides the foundation for intelligent device management in ODIS, enabling flexible and dynamic configuration of display devices without requiring manual setup or service restarts.