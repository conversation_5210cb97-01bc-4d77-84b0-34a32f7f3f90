# Onboard Display Protocol Implementation Specification

## Overview

This document specifies the migration from the current simple WebSocket broadcast implementation to the full Onboard Display Protocol as defined in `onboard-display-protocol.md`. The migration maintains simplicity while implementing all required protocol features.

## Current State Analysis

### ✅ Working Components (Keep Unchanged)
- **`data_coordinator.rs`**: Excellent data aggregation and deduplication logic
- **Channel Architecture**: Clean separation between data processing and WebSocket handling  
- **`devices.jsonc`**: Configuration file structure matches specification
- **Core Data Flow**: OnboardData generation and broadcasting concept

### ❌ Implementation Gaps

| Specification Requirement | Current Implementation | Status |
|---------------------------|----------------------|---------|
| Layered protocol architecture | Simple broadcast server | ❌ Missing |
| Device identification service | No identification | ❌ Missing |
| Message types with "type" field | Direct OnboardData serialization | ❌ Wrong format |
| Hello/Bye message handling | No client message processing | ❌ Missing |
| 3-second Hello timeout | No timeout handling | ❌ Missing |
| Error messages for unidentified devices | No error handling | ❌ Missing |
| Protocol state machine | Direct streaming | ❌ Missing |

## Implementation Plan

### Phase 1: Foundation (Week 1)

#### 1.1 Update Message Protocol (`src/onboard_protocol.rs`)

**Current Issue**: Direct OnboardData serialization doesn't match protocol specification.

**Solution**: Implement proper message types with "type" field.

```rust
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use crate::onboard_model::OnboardData;

// Server to Client Messages
#[derive(Serialize, Debug, Clone)]
#[serde(tag = "type")]
pub enum ProtocolMessage {
    #[serde(rename = "onboard-data")]
    OnboardData { onboard: OnboardData },
    
    #[serde(rename = "device-identification")]
    DeviceIdentification { 
        #[serde(rename = "deviceId")]
        device_id: String,
        #[serde(rename = "deviceClassifier")]
        device_classifier: String,
    },
    
    #[serde(rename = "error")]
    Error { 
        code: String, 
        message: String 
    },
}

// Client to Server Messages  
#[derive(Deserialize, Debug)]
#[serde(tag = "type")]
pub enum ClientMessage {
    #[serde(rename = "hello")]
    Hello {
        #[serde(rename = "displayId")]
        display_id: String,
        #[serde(flatten)]
        metadata: HashMap<String, serde_json::Value>,
    },
    
    #[serde(rename = "bye")]
    Bye { 
        reason: Option<String> 
    },
}

// Error codes defined by specification
pub mod error_codes {
    pub const DEVICE_NOT_IDENTIFIED: &str = "DEVICE_NOT_IDENTIFIED";
    pub const IDENTIFICATION_TIMEOUT: &str = "IDENTIFICATION_TIMEOUT";
    pub const CONFIG_ERROR: &str = "CONFIG_ERROR";
    pub const HANDSHAKE_FAILED: &str = "HANDSHAKE_FAILED";
    pub const CONNECTION_LOST: &str = "CONNECTION_LOST";
    pub const SEND_FAILED: &str = "SEND_FAILED";
}
```

#### 1.2 Create Device Identification Service (`src/device_identification.rs`)

**Requirements**: 
- Load device configuration from `devices.jsonc`
- Implement AND logic for device matching (all specified fields must match)
- Support IP address and displayId identification

```rust
use std::net::IpAddr;
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use tracing::{info, warn, debug};

#[derive(Debug, Clone, PartialEq)]
pub struct OnboardDeviceIdentification {
    pub device_id: String,
    pub device_classifier: String,
    pub connection_metadata: ConnectionMetadata,
}

#[derive(Debug, Clone, PartialEq)]
pub struct ConnectionMetadata {
    pub ip: IpAddr,
    pub display_id: Option<String>,
    // DNS hostname resolution can be added later
    pub dns: Option<String>,
}

#[derive(Deserialize, Debug)]
struct DeviceConfig {
    #[serde(default)]
    devices: Vec<DeviceEntry>,
}

#[derive(Deserialize, Debug)]
struct DeviceEntry {
    lookup: DeviceLookup,
    #[serde(rename = "device_id")]
    device_id: String,
    #[serde(rename = "device_classifier")]
    device_classifier: String,
}

#[derive(Deserialize, Debug)]
struct DeviceLookup {
    ip: Option<IpAddr>,
    dns: Option<String>,
    #[serde(rename = "display_id")]
    display_id: Option<String>,
}

pub struct DeviceIdentificationService {
    config: DeviceConfig,
}

impl DeviceIdentificationService {
    pub fn from_config_file(path: &str) -> Result<Self> {
        let content = std::fs::read_to_string(path)
            .map_err(|e| anyhow!("Failed to read device config file {}: {}", path, e))?;
        
        let config: DeviceConfig = serde_json::from_str(&content)
            .map_err(|e| anyhow!("Failed to parse device config JSON: {}", e))?;
        
        info!("Loaded {} device configurations", config.devices.len());
        Ok(Self { config })
    }

    /// Identify device using AND logic - all specified fields must match
    pub fn identify_device(&self, metadata: &ConnectionMetadata) -> Result<OnboardDeviceIdentification> {
        debug!("Attempting to identify device with metadata: {:?}", metadata);
        
        for (index, entry) in self.config.devices.iter().enumerate() {
            let mut matches = true;
            let mut match_criteria = Vec::new();
            
            // Check IP if specified (primary identification)
            if let Some(expected_ip) = entry.lookup.ip {
                match_criteria.push(format!("ip={}", expected_ip));
                if expected_ip != metadata.ip {
                    matches = false;
                    debug!("Device entry {} IP mismatch: expected {}, got {}", index, expected_ip, metadata.ip);
                }
            }
            
            // Check displayId if specified (from Hello message)
            if let Some(expected_display_id) = &entry.lookup.display_id {
                match_criteria.push(format!("displayId={}", expected_display_id));
                match &metadata.display_id {
                    Some(actual_display_id) if actual_display_id == expected_display_id => {
                        debug!("Device entry {} displayId match: {}", index, actual_display_id);
                    },
                    Some(actual_display_id) => {
                        matches = false;
                        debug!("Device entry {} displayId mismatch: expected {}, got {}", 
                               index, expected_display_id, actual_display_id);
                    },
                    None => {
                        matches = false;
                        debug!("Device entry {} displayId required but not provided", index);
                    }
                }
            }
            
            // Check DNS if specified (future enhancement)
            if let Some(expected_dns) = &entry.lookup.dns {
                match_criteria.push(format!("dns={}", expected_dns));
                // DNS resolution not implemented yet - will always fail if specified
                if metadata.dns.as_ref() != Some(expected_dns) {
                    matches = false;
                    debug!("Device entry {} DNS not implemented/mismatch", index);
                }
            }
            
            if matches {
                info!("Device identified: {} (criteria: {})", entry.device_id, match_criteria.join(", "));
                return Ok(OnboardDeviceIdentification {
                    device_id: entry.device_id.clone(),
                    device_classifier: entry.device_classifier.clone(),
                    connection_metadata: metadata.clone(),
                });
            }
        }
        
        warn!("No matching device configuration found for {:?}", metadata);
        Err(anyhow!("No matching device configuration found"))
    }

    pub fn device_count(&self) -> usize {
        self.config.devices.len()
    }
}
```

### Phase 2: Protocol State Machine (Week 2)

#### 2.1 Create Protocol Handler (`src/onboard_display_protocol.rs`)

**Requirements**:
- Implement state machine: AwaitDeviceHello → IdentifyingDevice → StreamingData
- Handle 3-second Hello timeout
- Process Hello and Bye messages
- Send appropriate error messages

```rust
pub struct OnboardDisplayProtocol {
    connection_id: ConnectionId,
    state: ProtocolState,
    device_info: Option<OnboardDeviceIdentification>,
    message_tx: UnboundedSender<ProtocolMessage>,
}

enum ProtocolState {
    AwaitDeviceHello {
        start_time: Instant,
        timeout_duration: Duration, // 3 seconds
        connection_metadata: ConnectionMetadata,
    },
    IdentifyingDevice {
        identification_service: Arc<DeviceIdentificationService>,
        hello_data: Option<ClientMessage>,
        connection_metadata: ConnectionMetadata,
    },
    StreamingData {
        device_info: OnboardDeviceIdentification,
        last_data_sent: Option<Instant>,
    },
    Terminated {
        reason: String,
    },
}
```

### Phase 3: Service Integration (Week 3)

#### 3.1 Create Protocol Manager (`src/protocol_manager.rs`)

**Requirements**:
- Manage multiple protocol instances
- Handle connection lifecycle  
- Coordinate data broadcasting
- Implement efficient receive handling with `tokio::select!`

#### 3.2 Update Service Entry Point (`src/service.rs`)

**Simplify to use ProtocolManager**:
- Load device configuration
- Create protocol manager
- Handle connections through manager
- Maintain data broadcasting channel

## Testing Strategy with websocat

### Basic Functionality Tests

#### Test 1: Simple Connection (IP-only identification)
```bash
# Expected: Wait 3s, then receive device-identification message
websocat ws://127.0.0.1:8080

# Expected output:
# {"type":"device-identification","deviceId":"door-left-display","deviceClassifier":"onboard-display-information"}
```

#### Test 2: Hello Message Enhancement
```bash
# Send Hello message immediately after connection
echo '{"type":"hello","displayId":"display_001"}' | websocat ws://127.0.0.1:8080

# Expected: Immediate device identification (no 3s wait)
```

#### Test 3: Invalid Device
```bash
# Connect from unregistered IP (modify devices.jsonc)
websocat ws://127.0.0.1:8080

# Expected output:
# {"type":"error","code":"DEVICE_NOT_IDENTIFIED","message":"Device could not be identified"}
```

#### Test 4: Multiple Concurrent Connections
```bash
# Test multiple devices connecting simultaneously
for i in {1..3}; do 
  websocat ws://127.0.0.1:8080 &
done
```

#### Test 5: Timeout Behavior
```bash
# Connect and wait without sending Hello - should timeout after exactly 3s
timeout 5 websocat ws://127.0.0.1:8080
```

#### Test 6: Bye Message
```bash
# Send Hello, then Bye
(echo '{"type":"hello","displayId":"display_001"}'; sleep 2; echo '{"type":"bye","reason":"testing"}') | websocat ws://127.0.0.1:8080
```

### Integration Test Script

Create `scripts/test_protocol.sh`:

```bash
#!/bin/bash
# Protocol Integration Test Suite

echo "=== Onboard Display Protocol Integration Tests ==="

SERVER_URL="ws://127.0.0.1:8080"

echo "Test 1: Basic connection with 3s timeout"
timeout 5 websocat $SERVER_URL | head -1 | grep -q "device-identification" && echo "✅ PASS" || echo "❌ FAIL"

echo "Test 2: Hello message (immediate identification)"
echo '{"type":"hello","displayId":"display_001"}' | timeout 2 websocat $SERVER_URL | head -1 | grep -q "device-identification" && echo "✅ PASS" || echo "❌ FAIL"

echo "Test 3: Data streaming format"
timeout 3 websocat $SERVER_URL | tail -1 | grep -q "onboard-data" && echo "✅ PASS" || echo "❌ FAIL"

echo "=== All tests completed ==="
```

## Success Criteria

### Functional Requirements ✅
- [ ] Device correctly identified based on IP + optional displayId (AND logic)
- [ ] Hello timeout works exactly after 3 seconds
- [ ] All messages have proper "type" field format as per specification
- [ ] Error messages sent for unidentified devices with correct error codes
- [ ] Multiple concurrent connections supported
- [ ] Graceful connection cleanup and resource management
- [ ] Hello and Bye messages processed correctly
- [ ] `data_coordinator.rs` remains unchanged and functional

### Protocol Compliance ✅
- [ ] All message formats match specification exactly
- [ ] State machine transitions work as defined (AwaitHello → Identifying → Streaming)
- [ ] Error codes match specification values
- [ ] WebSocket control frame handling (ping/pong/close)

## Implementation Timeline

| Week | Phase | Deliverables |
|------|-------|--------------|
| 1 | Foundation | Message types, Device identification service, Unit tests |
| 2 | Protocol | State machine, Timeout handling, Protocol tests |
| 3 | Integration | Service rewrite, Protocol manager, Integration tests |

## Migration Strategy

### What to Keep
- **`data_coordinator.rs`**: Works perfectly, no changes needed
- **Channel-based architecture**: Maintains clean separation
- **WebSocket transport layer**: Basic connection handling concepts
- **`devices.jsonc`**: Configuration structure matches spec

### What to Replace
- **`service.rs`**: Complete rewrite for protocol compliance
- **`onboard_protocol.rs`**: Update message types for "type" field
- **Connection handling**: Replace simple broadcast with protocol state machine

### What to Add
- **`device_identification.rs`**: New service for device lookup
- **`onboard_display_protocol.rs`**: New protocol state machine
- **`protocol_manager.rs`**: New connection lifecycle manager

## Migration Risks & Mitigation

### Risk 1: Breaking Existing Functionality
**Mitigation**: Keep `data_coordinator.rs` unchanged, maintain channel-based architecture

### Risk 2: Complex State Management
**Mitigation**: Comprehensive unit tests for state machine, simple state transitions

### Risk 3: WebSocket Connection Issues
**Mitigation**: Follow specification's efficient receive handling pattern, proper error handling

### Risk 4: Device Configuration Complexity
**Mitigation**: Start with IP-only identification, add features incrementally

## Rollback Plan

If migration fails:
1. Revert to original `service.rs` (keep backup)
2. Remove new modules (`device_identification.rs`, `onboard_display_protocol.rs`, `protocol_manager.rs`)
3. Restore original `onboard_protocol.rs`
4. Keep `data_coordinator.rs` unchanged (no rollback needed)

## Key Implementation Notes

### Simplicity Principles
1. **Keep Working Code**: `data_coordinator.rs` stays exactly the same
2. **Incremental Changes**: Implement phase by phase with testing
3. **Clear Separation**: Each layer has single responsibility
4. **Comprehensive Testing**: Unit tests + integration tests with websocat
5. **Error Handling**: Proper logging and graceful failures

### Performance Considerations
- Support at least 10 concurrent connections
- Message latency under 100ms for data broadcasting
- Memory usage stable over long-running sessions
- No connection leaks or resource exhaustion

### Architecture Benefits
- **Layered Design**: Clear separation of concerns as per specification
- **State Machine**: Predictable connection lifecycle
- **Device Identification**: Flexible configuration-based matching
- **Protocol Compliance**: Full adherence to onboard display protocol
- **Testing**: Comprehensive test coverage with realistic scenarios 