syntax = "proto3";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

package baselink;

service Baselink {
  // Returns a version string of the baselink server
  rpc GetVersion (google.protobuf.Empty) returns (Version);

  // Returns the time the baselink server was started
  // Intended to be used to identify the baselink server instance
  rpc GetStartupTime (google.protobuf.Empty) returns (google.protobuf.Timestamp);

  rpc SetBucketMetadata (BucketMetadata) returns (google.protobuf.Empty);
  rpc GetBucketMetadata (GetBucketMetadataRequest) returns (GetBucketMetadataResponse);

  // Replace the current timetable with the new one. Streamed in the form of individual TimetableEntries.
  rpc SetTimetable(stream TimetableEntry) returns (google.protobuf.Empty);


  rpc SetTrips(SetTripsRequest) returns (google.protobuf.Empty);
  rpc RemoveTrips(RemoveTripsRequest) returns (google.protobuf.Empty);

  rpc UpdateTrips(stream Trip) returns (google.protobuf.Empty);
  rpc SetAllTrips(stream Trip) returns (google.protobuf.Empty);

  rpc SetRefTrips(stream Trip) returns (google.protobuf.Empty);
  rpc UpdateRefTrips(stream Trip) returns (google.protobuf.Empty);

  rpc GetTrip(GetTripRequest) returns (Trip);
  rpc GetTripsByStartTime(GetTripsByStartTimeRequest) returns (TripsResponse);
  rpc GetActiveTrips(GetActiveTripsRequest) returns (TripsResponse);
  rpc GetActiveTripsStream(GetActiveTripsStreamRequest) returns (stream Trip);
  rpc CountActiveTrips(CountActiveTripsRequest) returns (CountActiveTripsResponse);

  rpc SubscribeTrips(SubscribeTripsRequest) returns (stream TripSubscriptionMessage);

  rpc SetDepartures(SetDeparturesRequest) returns (google.protobuf.Empty);
  rpc RemoveDepartures(RemoveDeparturesRequest) returns (google.protobuf.Empty);
  rpc GetDeparturesForStops(GetDeparturesForStopsRequest) returns (DeparturesResponse);

  rpc SetStops(stream StopPlace) returns (google.protobuf.Empty);
  rpc UpdateStops(StopList) returns (google.protobuf.Empty);

  // Given list of stop_ids return list of StopPlaces where
  // - stop_id belongs to one of the StopPlace StopPoints
  // - stop_id belongs to StopPlace itself
  //
  rpc GetStops(GetStopsRequest) returns (StopList);

  // Search stops:
  //  - geo search by bounding box matching any of StopPoint's
  //  - fuzzy search by stop name
  //  
  rpc SearchStops(SearchStopsRequest) returns (StopList);

  rpc SetLines(stream Line) returns (google.protobuf.Empty);
  rpc UpdateLines(LineList) returns (google.protobuf.Empty);
  rpc GetLinesById(GetLinesByIdRequest) returns (LineList); // unused
  rpc GetLinesByAgency(GetLinesByAgencyRequest) returns (LineList); // might be replaced by GetLines
  rpc GetLines(GetLinesRequest) returns (stream Line);

  rpc SetAgencies(AgencyList) returns (google.protobuf.Empty);
  rpc GetAgencies(GetAgenciesRequest) returns (AgencyList);

  rpc GetStopMessagesByStopId(GetStopMessagesByStopIdRequest) returns (StopMessageList);
  rpc GetAllStopMessages(GetAllStopMessagesRequest) returns (GetAllStopMessagesResponse);
  rpc SetStopMessages(SetStopMessagesRequest) returns (google.protobuf.Empty);
  rpc RemoveStopMessages(RemoveStopMessagesRequest) returns (google.protobuf.Empty);

  rpc GetLineLadders(GetLineLaddersRequest) returns (GetLineLaddersResponse);

  rpc SetVehicles(SetVehiclesRequest) returns (google.protobuf.Empty);
  rpc GetVehicles(google.protobuf.Empty) returns (GetVehiclesResponse);
  rpc GetVehicleTrip(GetVehicleTripRequest) returns (GetVehicleTripResponse);

  // Report service status
  //
  // ServiceStatus is set globally outside of buckets
  // service_name acts as unique identifier
  //
  // Once registered Service Owner is obliged to trigger service status with interval < 60 seconds
  //
  rpc ReportServiceStatus(ServiceStatusReport) returns (google.protobuf.Empty);
  rpc GetServiceStatuses(google.protobuf.Empty) returns (GetServiceStatusesResponse);

  rpc RemoveServiceStatus(RemoveServiceStatusRequest) returns (google.protobuf.Empty);
}

message BucketMetadata {
  string bucket_id = 1;

  // Sources can denote a data version that the data in the bucket is based on.
  // It is not guaranteed that this version changes anytime data changes. Instead it
  // can be used to signal a change in the base data that might make it inconsistent
  // with previously seen data. Data sinks like VDV-realtime-server use this to signal
  // to clients that they need to re-establish all subscriptions and re-fetch all data.
  string data_version_id = 2;
}

message GetBucketMetadataRequest {
  repeated string bucket_ids = 1;
}

message GetBucketMetadataResponse {
  repeated BucketMetadata metadata = 1;
}

message RemoveServiceStatusRequest {
  string service_name = 1;
}

message GetServiceStatusesResponse {
  repeated ServiceStatus service_statuses = 1;
}

message ServiceStatus {
  int64 last_update_time = 1;
  ServiceStatusReport report = 2;
}

message ServiceStatusReport {
  // name works as identifier
  //
  // application is responsible for creating unique name
  // service name is presentable string
  string name = 1;

  ServiceState state = 5;
  string reason = 6;
  ServiceKind kind = 7;

  // Reported service state
  enum ServiceState {
    SERVICE_STATE_UNSPECIFIED = 0;
    SERVICE_STATE_OK = 1;
    SERVICE_STATE_ERROR = 2;
    SERVICE_STATE_UNAVAILABLE = 3;
  };

  enum ServiceKind {
    // UNSPECIFIED defaults to OTHER; default
    SERVICE_KIND_UNSPECIFIED = 0;
    SERVICE_KIND_DATA_INPUT = 1;
    SERVICE_KIND_DATA_OUTPUT = 2;
    SERVICE_KIND_DATA_PROCESSOR = 3;
  };
}

message GetStopMessagesByStopIdRequest {
  repeated string stop_ids = 1;
}

message GetAllStopMessagesRequest {
  Filter bucket_id_filter = 1;
}

message GetAllStopMessagesResponse {
  repeated BucketStopMessages bucket_messages = 1;
}

message BucketStopMessages {
  string bucket_id = 1;
  repeated StopMessage stop_messages = 2;
}


message RemoveStopMessagesRequest {
  repeated string stop_message_ids = 1;
}

message SetStopMessagesRequest {
  repeated StopMessage stop_messages = 1;

  Mode mode = 2;

  enum Mode {
    REPLACE_STOP_MESSAGE = 0;
    REPLACE_BUCKET = 1;
  }
}

message StopMessageList {
  repeated StopMessage stop_messages = 1;
}

message StopMessagesResponse {
  repeated StopMessage stop_messages = 1;
}

message StopMessage {
  string id = 1;
  repeated string stop_ids = 2;
  string text = 3;
  string kind = 4;

  optional int64 start_active_time = 5;
  optional int64 end_active_time = 6;

  repeated TimeOfDayRange time_of_day_filter = 7;
  WeekdaySet weekday_filter = 8;
}

message WeekdaySet {
  bool monday = 1;
  bool tuesday = 2;
  bool wednesday = 3;
  bool thursday = 4;
  bool friday = 5;
  bool saturday = 6;
  bool sunday = 7;
  // IANA timezone identifier
  optional string timezone = 8; // IANA
}

message TimeOfDayRange {
  uint32 starts_at = 1;
  uint32 ends_at = 2;
  // IANA timezone identifier
  optional string timezone = 3;
}

message GetStopsRequest {
  repeated string stop_ids = 1;
}

message SearchStopsRequest {
  // Performs a fuzzy search for stops by name based on a user-provided query.
  //
  // It returns a sequence of baselink_api::Stop messages. The number of results returned is controlled
  // by the max_results parameter in the SearchStopsRequest message.
  //
  // The results are first sorted by rank, from the best match to the worst. In case of ties, the results are
  // further sorted alphabetically by name.
  //
  // When the query string is empty (""), it returns stops sorted in alphabetical order, respecting the limit
  // specified by max_results.
  //
  // This function is primarily intended for use in user-facing search features in the UI.

  string query = 1;  // The search string provided by the user.
  uint32 max_results = 2;  // The maximum number of results to return.
  optional GeoBoundingBox area = 3;
}

message GeoBoundingBox {
  LatLng top_left = 1;
  LatLng bottom_right = 2;
}

message StopList {
  repeated StopPlace stops = 1;
}

// StopPlace generalizes concept of Station, city traffic Stop, Stop group,
// Stop area Names a group StopPoints functional locations
//
// StopPlace is not precise location, rather it is an area.
message StopPlace {
  // if data source has no parent stop places then create artifical id
  string id = 1;
  string name = 2;

  // intended to be and approximate position of a StopPlace for an overview
  optional LatLng position = 3;
  repeated StopPoint stop_points = 4;
}

// A functional point on route. Halts, Departures refer to this type
message StopPoint {
  // Unique id of a stop point
  // Keep original datasource id.
  // Warning!
  //  - the space of ids is shared with StopPlace.id
  string id = 1;

  // usually a short name for a platform or stop point like: 1
  //
  optional string name = 2;

  // location of an alighting/departing point
  optional LatLng position = 3;
}

// compatible with https://github.com/googleapis/googleapis/blob/master/google/type/latlng.proto
message LatLng {
  double latitude = 1;
  double longitude = 2;
}

message Version {
  uint64 major = 1;
  uint64 minor = 2;
  uint64 patch = 3;
}



// Filters elements by being applied to an attribute of the element
message Filter {
  FilterMode mode = 1;
  repeated string values = 2;

  enum FilterMode {
    // exclude elements where given attribute is equal to one of values
    EXCLUDE_EQUAL = 0;

    // include only elements where given attribute is equal to one of values
    INCLUDE_EQUAL = 1;

    // include only elements where given attribute contains one of values (case insensitive)
    INCLUDE_CONTAINS = 2;

    // include only elements where given attribute starts with one of values (case insensitive)
    INCLUDE_STARTS_WITH = 3;
  }
}

message ListBucketsResponse {
  repeated string bucket_ids = 1;
}

message TimetableEntry {
  oneof payload {
    TimetableService service = 1;
    TimetableTrip trip = 2;
    TimetableHalt halt = 3;
  }
}

message TimetableService {
  string id = 1;
  // each operating day is represented by its start time as unix timestamp
  repeated int64 operating_days = 2;
}


message TimetableTrip {
  string id = 1;
  string service_id = 2;
  string line_id = 3;
  string block_id = 4;
  string course_id = 5;
  string direction_id = 6;
  string destination_text = 7;
}

message TimetableHalt {
  // offset is in seconds from operating day
  string trip_id = 1;
  uint32 sequence = 2;
  string stop_id = 3;
  uint32 arrival_offset = 4;
  uint32 departure_offset = 5;
  string destination_text = 6;

  // Previous versions required trip_id to only be unique within a service.
  // This meant trip needed to be identified using trip_id and service_id.
  // Current version requires trip_id to be unique on its own.
  reserved 11; //  string service_id = 11;

}


message SetTripsRequest {
  enum Mode {
    REPLACE_TRIP = 0;
    REPLACE_BUCKET = 1;
  }

  Mode mode = 1;
  repeated Trip trips = 2;
}

enum Occupancy {
  OCCUPANCY_UNSPECIFIED = 0;
  OCCUPANCY_LOW = 1;
  OCCUPANCY_MODERATE = 2;
  OCCUPANCY_HIGH = 3;
}

message Car {
  string id = 1;
  Occupancy occupancy = 2;
}

message CarGroup {
  string id = 1;
  repeated Car cars = 2;
  string destination_text = 3;
}

message VehicleStructure {
  // This message describes the structure of vehicle and how it arrives at the platform.
  // A vehicle can consist of multiple cars, which in turn can be grouped.
  // Ordering: If backwards is false then
  // - first group in vehicle_groups is the leading group with respect to travel direction
  // - first vehicle of group is the leading vehicle withing a group
  //
  // If backwards is true, then all of that is reversed
  // For example, the following Vehicle has 6 cars in 2 groups.
  //  <- [1 2 3][4 5 6]
  //       G1     G2

  reserved 1; // if we need to add an ID later
  reserved 2; // for future use

  repeated CarGroup car_groups = 3;
  bool backwards = 4;
}


message Trip {
  string id = 1;
  int64 operating_day = 2;
  string destination_text = 3;
  string line_id = 4;
  string line_name = 5;
  string block_id = 6;
  string course_id = 7;
  string direction_id = 8;
  string product_id = 9;
  string agency_id = 10;
  string vehicle_type_id = 11;
  string vehicle_id = 12;
  string product_text = 13;

  repeated string advisory_messages = 15;

  bool is_cancelled = 20;
  bool wheelchair_accessible = 21;

  repeated Halt halts = 30;

  string convoy_id = 40;
}



enum TimeStatus {
  // enum unset
  TIME_STATUS_UNSPECIFIED = 0;

  // time is in the future and a prediction
  TIME_STATUS_PREDICTION = 1;

  // time is in the past and is a real observed or otherwise precise measure
  TIME_STATUS_PAST_PRECISE = 2;

  // time is in the past, but an estimate, for example from interpolating after getting a vehicle update
  TIME_STATUS_PAST_ESTIMATE = 3;

  // the time is unknown, only valid if the time that this status is about is also unset
  TIME_STATUS_UNKNOWN = 4;
}

enum PredictionIssue {
  // enum is unset
  PREDICTION_ISSUE_UNSPECIFIED = 0;

  // all is ok
  PREDICTION_ISSUE_NONE = 1;

  // there is an issue, but unkown reason
  PREDICTION_ISSUE_UNKNOWN = 2;

  // vehicle is in a traffic jam
  PREDICTION_ISSUE_TRAFFIC_JAM = 3;

  // technical problem with the prediction system
  PREDICTION_ISSUE_TECHNICAL_PROBLEM = 4;

  // dispatch is intervening with the trip, for example instructing driver to wait for connecting service
  PREDICTION_ISSUE_DISPATCHING_MEASURE = 5;

  // the prediction system has not received updates about the vehicle position for a while
  PREDICTION_ISSUE_MISSING_UPDATES = 6;
}

message Halt {
  string stop_id = 1;
  uint32 seq_number = 2;

  int64 scheduled_arrival_time = 3;
  int64 scheduled_departure_time = 4;
  int64 actual_arrival_time = 5;
  int64 actual_departure_time = 6;
  TimeStatus arrival_status = 7;
  TimeStatus departure_status = 8;
  PredictionIssue prediction_issue = 9;

  string destination_text = 20;
  string platform_name = 21;
  string stop_name = 22;
  repeated string advisory_messages = 23;
  repeated ViaText via_texts = 24;
  VehicleStructure vehicle_structure = 25;

  bool pass_through = 40;
  bool no_entry = 41;
  bool no_exit = 42;
}

message GetTripRequest {
  string trip_id = 1;
  int64 operating_day = 2;
}

message RemoveTripsRequest {
  repeated DatedTripId trips = 1;
}

message DatedTripId {
  string trip_id = 1;
  int64 operating_day = 2;
}



message TripSelector {
  // Select only trips that match one of these line_ids. Leave empty to ignore
  repeated string included_line_ids = 1;

  // reserved for adding excluded_line_ids later
  reserved 2;

  // Select only trips that match one of these agency_ids. Leave empty to ignore
  repeated string included_agency_ids = 3;
}

message BucketSelector {
  // When set to true, only buckets present the bucket_id list are selected.
  // When set to false, only buckets *NOT* present the bucket_id list are selected.
  bool is_inclusive = 1;

  // Select buckets with id either included or not included in this list, depending on value of "exclude_matching"
  repeated string bucket_ids = 2;
}

message GetActiveTripsRequest {

  // return trips start are active at the following time
  int64 active_at_time = 1;

  // if given, returns trips that are active somewhere in the range
  optional int64 active_at_range_end = 2;

  // max number of results to be returned, must not be higher then 1000
  uint32 max_results = 3;

  // only return trips matching the following filters
  Filter agency_id_filter = 4;
  Filter line_name_filter = 5;
  Filter destination_text_filter = 6;
  Filter line_id_filter = 7;

  // Skip the first x results. This allows for paginated access to the results
  uint32 result_start_index = 20;
}

message CountActiveTripsRequest {
  int64 active_at_time = 1;
  Filter agency_id_filter = 2;
}

message CountActiveTripsResponse {
  uint32 active_trip_count = 1;
}

message SubscribeTripsRequest {
  int64 window_length_s = 2;

  reserved 3;

  BucketSelector bucket_selector = 4;
}

message TripSubscriptionMessage {

  // Events that update the state of the monitored window.
  // We differentiate two different kinds of trips being removed from the
  // window: left_window and removed. The reason is that some receives handle
  // those events differently. For example, in VDV454 a trip that leaves the
  // monitored window is just no longer updated. A trip that is removed causes
  // either a "trip-reset" or even a reset of the entire subscription.
  // For trips that are added we don't need to differntiate the cases, since
  // there are no different actions to be taken. Its always just setting the
  // entire trip.
  oneof payload {
    Trip added = 1;
    Trip updated = 2;
    DatedTripId left_window = 3;
    DatedTripId removed = 4;

    // Marker sent when a batch of messages is completed. Often changes come in groups and this marks the end for
    // such groups of changes. This is useful for some data consumers (for example VDV454) that cache data and flush
    // it based on this marker. It can be ignored otherwise.
    google.protobuf.Empty batch_end = 5;
  }
}

message GetActiveTripsStreamRequest {
  BucketSelector buckets = 1;
  TripSelector selector = 2;
  int64 active_from = 3;
  int64 active_until = 4;
}

message GetTripsByStartTimeRequest {
  int64 start_time_from = 1;
  int64 start_time_until = 2;
  reserved 3; // trips filter [removed]
  uint32 max_results = 4;
  reserved 5; // bucket filter
  reserved 6; // old agency_id_filter, use selector instead

  TripSelector selector = 10;
}

message TripsResponse {
  repeated Trip trips = 1;
}


message Departure {
  // id only has to be unique in combination with stop_id. The id field must be set in calls to SetDepartures.
  string id = 1;

  string stop_id = 2;
  string destination_text = 3;
  string line_name = 4;
  string line_id = 5;
  string trip_id = 6;
  int64 operating_day = 7;
  string block_id = 8;
  string platform_name = 9;
  string agency_id = 10;
  int64 trip_start_time = 11;
  string trip_start_stop_id = 12;
  string trip_end_stop_id = 13;
  string original_platform = 14;

  int64 scheduled_arrival_time = 20;
  int64 scheduled_departure_time = 21;
  int64 actual_arrival_time = 22;
  int64 actual_departure_time = 23;

  repeated string advisory_messages = 30;
  string cancellation_reason = 31;
  reserved 32; // old via texts
  repeated ViaText via_texts = 33;
  VehicleStructure vehicle_structure = 34;

  bool is_departed = 40;
  bool is_cancelled = 41;
  bool at_stop = 42;
  bool traffic_jam = 43;
  bool pass_through = 44;
  bool no_entry = 45;
  bool no_exit = 46;
  bool is_last_trip_halt = 47;
  bool wheelchair_accessible = 48;
  bool platform_changed = 49;
}

message ViaText {
  string text = 1;
  optional int32 priority = 2;
}

message SetDeparturesRequest {
  enum Mode {
    REPLACE_DEPARTURE = 0;
    REPLACE_STOP = 1;
    REPLACE_BUCKET = 2;
  };

  Mode mode = 1;
  repeated Departure departures = 2;
}


message DepartureUid {
  string departure_id = 1;
  string stop_id = 2;
}

message RemoveDeparturesRequest {
  repeated DepartureUid departures = 2;
}


enum DepartureOrder {
  DEPARTURE_ORDER_PREDICTED_TIME = 0;
  // extension point
}

message GetDeparturesForStopsRequest {
  repeated string stop_ids = 1;
  uint32 max_results = 2;
  ResultsLimitScope results_scope = 8;
  int64 time_from = 3;
  int64 time_until = 4;
  reserved 5; // old empty departure filter
  reserved 6; // bucket filter
  DepartureOrder order_by = 7;

  optional DepartureSelector selector = 10;

  enum ResultsLimitScope {
    RESULTS_LIMIT_SCOPE_TOTAL = 0;
    RESULTS_LIMIT_SCOPE_STOP = 1;
  }
}

message DepartureSelector {
  repeated string included_line_ids = 1;
  repeated string excluded_line_ids = 2;
}

message DeparturesResponse {
  repeated Departure departures = 1;
}


message Color {
  // The amount of red in the color as a value in the interval [0, 1].
  float red = 1;

  // The amount of green in the color as a value in the interval [0, 1].
  float green = 2;

  // The amount of blue in the color as a value in the interval [0, 1].
  float blue = 3;


  // The fraction of this color that should be applied to the pixel. That is,
  // the final pixel color is defined by the equation:
  //
  //   `pixel color = alpha * (this color) + (1.0 - alpha) * (background color)`
  //
  // This means that a value of 1.0 corresponds to a solid color, whereas
  // a value of 0.0 corresponds to a completely transparent color. This
  // uses an optional float so that it is possible to distinguish between a
  // default value and the value being unset.
  // If omitted, this color object is rendered as a solid color
  // (as if the alpha value had been explicitly given a value of 1.0).
  optional float alpha = 4;
}

message Line {
  string id = 1;
  string name = 2;
  string agency_id = 3;
  string long_name = 4;
  Color color = 5;
  Color text_color = 6;
}

message GetLinesRequest {
  Filter agency_id_filter = 1;
  Filter line_name_filter = 2;
}

message GetLinesByIdRequest {
  repeated string line_ids = 1;
}

message GetLinesByAgencyRequest {
  string agency_id = 1;
}

message LineList {
  repeated Line lines = 1;
}

message Agency {
  string id = 1;
  string name = 2;
  string url = 3;
  string language = 4;
  string timezone = 5;
}

message AgencyList {
  repeated Agency agencies = 1;
}

message GetAgenciesRequest {
}


message GetLineLaddersRequest {
  repeated string line_ids = 1;
}

message GetLineLaddersResponse {
  repeated LineLadder ladders = 1;
}

message LineLadder {
  string line_id = 1;
  repeated LineLadderEntry entries = 2;
}

message LineLadderEntry {
  string stop_name = 1;
  string stop_place_id = 2;

  repeated string direction_1_stop_ids = 4;
  repeated bool direction_1_routes = 5;

  repeated string direction_2_stop_ids = 6;
  repeated bool direction_2_routes = 7;
}


message Vehicle {
  string id = 1;
  int64 operating_day = 2;
  string block_id = 3;
  string trip_id = 4;
  string course_id = 5;
  string line_id = 6;
  optional LatLng gps_position = 7;
}

message SetVehiclesRequest {
  string bucket = 1;
  repeated Vehicle vehicles = 2;
}

message GetVehiclesResponse {
  repeated Vehicle vehicles = 1;
}

message GetVehicleTripRequest {
  string vehicle_id = 1;
}

message GetVehicleTripResponse {
  Vehicle vehicle = 1;
  optional Trip current_trip = 2;
}


