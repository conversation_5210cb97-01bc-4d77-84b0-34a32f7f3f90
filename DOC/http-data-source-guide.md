# HTTP Data Source Guide

This guide covers the HTTP data source implementation in ODIS, which provides comprehensive simulation capabilities for development, testing, and debugging of onboard journey data.

## Overview

The HTTP data source is one of two primary data source implementations in ODIS:

- **HTTP Data Source**: Manual journey loading and simulation control (this guide)
- **Baselink Data Source**: Automatic trip fetching from external Baselink systems

## HTTP Data Source Features

### Simulation Capabilities

- **Manual Journey Loading**: Load journey data via HTTP API or web interface
- **Playback Control**: Play, pause, and speed adjustment (1x-10x)
- **Time Manipulation**: Set specific timestamps and navigate through journey timeline
- **State Navigation**: Fine-grained control over journey states and stop progression
- **Real-time Updates**: WebSocket streaming of simulation state to connected displays

### Web Management Interface

- **Trip Loading Interface**: Load journeys from Baselink or manual JSON files
- **Simulation Dashboard**: Real-time control and monitoring of journey simulation
- **Journey Visualization**: Timeline view of stops, states, and progress
- **Connection Monitoring**: Status of WebSocket connections and data flow

## Getting Started

### 1. Start the Server in HTTP Mode

```bash
cargo run -- --data-source http --vehicle-id test-vehicle-123
```

The server will start:
- HTTP API server on `http://localhost:8080`
- WebSocket server on `ws://localhost:8765` (for real-time data streaming)
- Web management interface at `http://localhost:8080` (embedded)

### 2. Access the Web Interface

Open your browser to `http://localhost:8080` to access the management interface with:
- **Trip Loading**: Load journeys from Baselink or JSON files
- **Simulation Control**: Control playback, time, and navigation
- **Real-time Monitoring**: View current simulation state and connected devices

## HTTP API Endpoints

### Data Loading

#### Load Journey from JSON
```bash
curl -X POST http://localhost:8080/api/data/load/journey \
  -H "Content-Type: application/json" \
  -d @test_journey.json
```

#### Get Available Data Sources
```bash
curl -X GET http://localhost:8080/api/data/sources
```

### Simulation Control

#### Get Current State
```bash
curl -X GET http://localhost:8080/api/simulation/state
```

#### Playback Control
```bash
# Start/Resume playback
curl -X POST http://localhost:8080/api/simulation/resume \
  -H "Content-Type: application/json" \
  -d '{"speed_multiplier": 4}'

# Stop playback
curl -X POST http://localhost:8080/api/simulation/stop
```

#### Time Control
```bash
# Set specific time
curl -X POST http://localhost:8080/api/simulation/time \
  -H "Content-Type: application/json" \
  -d '{"timestamp": 1735300900}'
```

#### Navigation Control
```bash
# State navigation (fine control)
curl -X POST http://localhost:8080/api/simulation/next-state
curl -X POST http://localhost:8080/api/simulation/previous-state

# Stop navigation (coarse control)
curl -X POST http://localhost:8080/api/simulation/next-stop
curl -X POST http://localhost:8080/api/simulation/previous-stop
```

### Baselink Integration (Optional)

When a Baselink URL is provided, the HTTP data source can also load trips from Baselink:

```bash
# Start with Baselink integration
cargo run -- --data-source http --baselink-url https://baselink-server:50051 --vehicle-id test-vehicle-123
```

#### Baselink Endpoints
```bash
# Get connection status
curl -X GET http://localhost:8080/api/baselink/status

# Get available vehicles
curl -X GET http://localhost:8080/api/baselink/vehicles

# Get trip by ID
curl -X GET http://localhost:8080/api/baselink/trips/422250752
```

## Journey Data Format

### Example Journey JSON

```json
{
  "journey": {
    "tripId": "bus-line-7-trip-42",
    "destinationText": "Central Station",
    "lineName": "Line 7",
    "calls": [
      {
        "stopId": "stop_001",
        "seqNumber": 1,
        "scheduledArrivalTime": 1735300800,
        "scheduledDepartureTime": 1735300800,
        "actualArrivalTime": null,
        "actualDepartureTime": 1735300805,
        "destinationText": "Central Station",
        "platformName": "A1",
        "stopName": "Airport Terminal",
        "advisoryMessages": ["Welcome aboard"],
        "isCancelled": false,
        "exitSide": "Right"
      }
    ]
  }
}
```

### Field Descriptions

- **tripId**: Unique identifier for the journey
- **destinationText**: Final destination display text
- **lineName**: Line or route identifier
- **calls**: Array of stops in the journey
- **seqNumber**: Stop sequence number (1-based)
- **scheduledArrivalTime/scheduledDepartureTime**: Unix timestamps
- **actualArrivalTime/actualDepartureTime**: Actual times (null if not yet occurred)
- **stopName**: Display name of the stop
- **platformName**: Platform or bay identifier
- **advisoryMessages**: Array of messages to display
- **isCancelled**: Whether this stop is cancelled
- **exitSide**: Which side doors open ("Left", "Right", "Both")

## Simulation States

The simulation tracks four location states as the journey progresses:

- **BetweenStops**: Traveling between stops
- **BeforeStop**: Approaching a stop
- **AtStop**: Currently at a stop
- **AfterStop**: Just left a stop

State progression: `BetweenStops` → `BeforeStop` → `AtStop` → `AfterStop` → `BetweenStops`

## Speed Multiplier

Valid range: 1-10
- `1`: Real-time speed
- `2-5`: Moderate acceleration for testing
- `6-10`: High-speed testing

## WebSocket Integration

The HTTP API controls the simulation, while the WebSocket server broadcasts real-time data updates:

1. Use HTTP API to control the simulation
2. Connect WebSocket clients to receive live updates
3. Both work simultaneously for comprehensive testing

**WebSocket URL**: `ws://localhost:8765`

## Use Cases

### Development and Testing

- **Journey Simulation**: Test display behavior with realistic journey data
- **State Testing**: Verify display updates for different journey states
- **Timing Validation**: Test schedule adherence and delay scenarios
- **Message Testing**: Validate advisory message display and formatting

### Debugging

- **State Inspection**: Examine current simulation state and data
- **Timeline Navigation**: Jump to specific points in journey for debugging
- **Connection Testing**: Monitor WebSocket connections and data flow
- **Error Simulation**: Test error handling with invalid data or states

### Integration Testing

- **API Validation**: Test HTTP API endpoints and response formats
- **WebSocket Testing**: Validate real-time data streaming
- **Multi-client Testing**: Test multiple display connections simultaneously
- **Performance Testing**: Stress test with high-speed simulation

## Best Practices

### Journey Data

- Use realistic timestamps and stop sequences
- Include variety in advisory messages and stop types
- Test with both normal and edge case scenarios (delays, cancellations)
- Validate JSON format before loading

### Simulation Control

- Start with slower speeds (1x-2x) for detailed observation
- Use state navigation for precise testing of specific scenarios
- Monitor WebSocket connections when testing multiple displays
- Save interesting journey configurations for reuse

### API Usage

- Check response status codes and error messages
- Use appropriate Content-Type headers for POST requests
- Handle network timeouts and connection errors gracefully
- Validate API responses before processing data

---

This guide provides comprehensive coverage of the HTTP data source capabilities for development, testing, and debugging of ODIS functionality.
