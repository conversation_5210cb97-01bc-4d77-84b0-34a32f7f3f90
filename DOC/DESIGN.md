# Architecture and Design Decisions Log

## Data Source Architecture

### Current Implementation

ODIS supports configurable data sources through a pluggable architecture:

- **Single Active Source**: Only one data source is enabled at runtime
- **Unified Data Model**: All sources convert to a common onboard data model
- **Protocol Agnostic**: Sources can use different protocols (gRPC, HTTP, file, etc.)

### Supported Data Sources

#### Baselink Data Source

- **Purpose**: Production deployment with real vehicle/trip data
- **Protocol**: gRPC connection to external Baselink systems
- **Features**: Automatic trip fetching, real-time polling, error handling

#### HTTP Data Source

- **Purpose**: Development, testing, and debugging
- **Protocol**: Internal simulation state managed via HTTP API
- **Features**: Manual journey control, time manipulation, web interface

### Future Data Sources

- **IBIS-IP**: Planned integration with IBIS-IP protocol
- **File-based**: Static journey data from files
- **Custom Protocols**: Extensible architecture for new data sources

## Device Identification and Context

The Onboard Data Server provides device identification services to connected displays:

1. **Device Detection**: Identifies connecting devices using configurable rules
2. **Context Assignment**: Provides device-specific metadata (type, location, configuration)
3. **Dynamic Updates**: Supports runtime configuration changes without reconnection

### Vehicle Context Support

ODIS is designed to support various vehicle configurations:

```text
 |                         train                              |
          | a cars group           | |   another cars group   |
  ______   ______   ______   ______   ______   ______   ______
 /  __  |.|  __  |.|  __  |.|  __  |.|  __  |.|  __  |.|  __  |
 `^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^' '^^  ^^'
          | car  |  or                        | coach|

EMU - train consist/ train set
 |                         train                                 |
 |  EMU                         | |            EMU               |
  ______  ______  ______  ______   ______  ______  ______  ______
 /  __  ||  __  ||  __  ||  __  \_/  __  ||  __  ||  __  ||  __  \
 `^^  ^^''^^  ^^''^^  ^^''^^  ^^' `^^  ^^''^^  ^^''^^  ^^''^^  ^^'
          | car  |  or                        | coach|
```

## Data Processing Pipeline

### Core Components

- **Data Coordinator**: Central hub that processes incoming data and manages state
- **Data Sources**: Pluggable adapters for different data inputs
- **Device Configuration Service**: Manages device identification rules
- **WebSocket Server**: Real-time data distribution to displays
- **HTTP API Server**: Management interface and simulation control

### Data Flow

1. **Device Connection**: Device connects to server over WebSocket
2. **Device Identification**: Server identifies device and sends identification metadata
3. **Data Ingestion**: Data comes from configured source (Baselink, HTTP simulation, etc.)
4. **Data Processing**: Data coordinator processes and normalizes data
5. **Data Distribution**: Processed data is fanned out to connected devices
6. **State Management**: Current state is maintained for new device connections

## Operational Modes

### Data Source Configuration

The application operates with a single active data source, configured at startup:

**Baselink Mode:**
```bash
cargo run -- --data-source baselink --baselink-url https://baselink-server:50051 --vehicle-id vehicle-123
```

**HTTP Simulation Mode:**

```bash
cargo run -- --data-source http --vehicle-id test-vehicle-123
```

## Data Domains

### Core Data Types

- **Journey Information**: Trip details, stops, schedules, real-time updates
- **Vehicle Information**: Vehicle ID, coach number, door status, physical configuration
- **Location Status**: Current position relative to stops (BetweenStops, BeforeStop, AtStop, AfterStop)

### Unified Data Model

All data sources convert their native formats into a unified onboard data model:

```rust
pub struct OnboardData {
    pub current_time: Timestamp,
    pub vehicle_information: VehicleInformation,
    pub vehicle_journey: Option<VehicleJourney>,
}
```

## Device Configuration Service

### Configuration Management

- **Static Configuration**: JSON-based device identification rules
- **Dynamic Updates**: Runtime configuration changes via HTTP API
- **Rule Evaluation**: Flexible matching based on device properties

### Device Identification Process

1. Device connects and provides identification data
2. Server evaluates device against configured rules
3. Server responds with device context (type, location, layout assignment)
4. Device uses context to determine appropriate behavior

## Display Integration Process

### WebDisplay Client Flow

1. **Initial Load**: WebDisplay loads from external WebDisplay server
2. **Connection**: WebDisplay connects to ODIS over WebSocket
3. **Identification**: ODIS provides device identification metadata
4. **Configuration**: Device receives layout and configuration assignments
5. **Data Streaming**: ODIS streams real-time onboard data
6. **Updates**: Dynamic configuration changes handled without reconnection

### Dynamic Configuration Changes

- **Real-time Updates**: Configuration changes applied to active connections
- **No Reconnection Required**: Device identification messages sent to existing connections
- **Graceful Handling**: WebDisplay clients handle configuration updates automatically

## Web Management Interface

### Current Implementation

The web interface provides comprehensive management capabilities:

- **Trip Loading**: Load journeys from Baselink or manual JSON files
- **Simulation Control**: Real-time control of journey simulation
- **Device Monitoring**: View connected devices and their status
- **Configuration Management**: Manage device identification rules

### Key Features

- **Embedded Deployment**: Web assets embedded in Rust binary
- **Real-time Updates**: WebSocket integration for live data
- **Development Tools**: Debugging and testing capabilities
- **Production Ready**: Single binary deployment with web interface

## Technology Stack

### Core Technologies

- **Runtime**: Rust with Tokio async runtime
- **WebSocket**: tokio-tungstenite for real-time communication
- **HTTP API**: Axum web framework
- **Time Handling**: jiff for modern time operations
- **Error Handling**: anyhow for comprehensive error management
- **Frontend**: React + TypeScript embedded via rust-embed

### Design Principles

- **Single Binary Deployment**: All components embedded in one executable
- **Pluggable Architecture**: Extensible data source system
- **Real-time First**: WebSocket-based communication for low latency
- **Configuration Driven**: Flexible device identification and routing
- **Development Friendly**: Comprehensive simulation and debugging tools

---

This design log reflects the current implementation and architectural decisions as of 2025.