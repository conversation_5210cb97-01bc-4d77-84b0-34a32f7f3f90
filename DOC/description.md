# Onboard Display Information Server (ODIS) — Architecture Specification

## 1. Introduction

### 1.1 Purpose

This document defines the architecture and behavior of the Onboard Display Information Server (ODIS), a dual-purpose server application that provides:

1. **Device Identification Service**: When client display devices connect, they receive metadata about themselves (device ID, classifier, etc.) which helps them determine appropriate configuration and behavior.
2. **Data Ingestion and Distribution**: The server ingests data from external sources, transforms it into a unified "onboard data model," and distributes it to connected display devices.

### 1.2 Scope

- Describe system components and their interactions
- Specify service endpoints, data models and configuration layers
- Outline startup, runtime and error‑handling behavior
- Document current data source implementations (Baselink and HTTP)
- Provide a baseline for developers and system integrators

---

## 2. Overall Architecture

```mermaid
graph TD
    subgraph "Outside Vehicle Network"
        WDS[WebDisplay Server]
        BLS[Baselink Server]
    end

    subgraph "Inside Vehicle Network"
        BBDC[Browser-based Display Clients]
        ODIS["Onboard Display Information Server (ODIS)"]
        WUI[Web UI - Management Interface]
    end

    BBDC -- "(1) Load WebDisplay Frontend" --> WDS
    WDS -- "(2) Serves web app, layouts, config
    [WebSocket & HTTP(S)]" --> BBDC
    BBDC -- "(3) Connect WebSocket" --> ODIS
    ODIS -- "(4) Device Identification
    & Real-time Data [WebSocket]" --> BBDC

    WUI -- "(5) Management & Control
    [HTTP API]" --> ODIS
    ODIS -- "(6) Simulation Control
    & Status [HTTP]" --> WUI

    ODIS -- "(7) Poll/Fetch Trip Data
    [gRPC - Baselink Mode]" --> BLS
    BLS -- "Trip & Vehicle Data" --> ODIS

    Note1[HTTP Data Source Mode:<br/>Manual journey loading<br/>& simulation control]
    Note2[Baselink Data Source Mode:<br/>Automatic trip fetching<br/>from external system]
```

### 2.1 Data Source Architecture

ODIS supports two configurable data source implementations:

#### Baselink Data Source

- **Purpose**: Connects to external Baselink systems via gRPC API
- **Use Case**: Production deployment with real vehicle/trip data
- **Features**:
  - Automatic trip fetching based on vehicle ID
  - Real-time data polling from central systems
  - Transforms stationary passenger information into onboard data model
  - Handles connection failures gracefully

#### HTTP Data Source

- **Purpose**: Provides comprehensive simulation capabilities
- **Use Case**: Development, testing, and debugging
- **Features**:
  - Manual journey loading via HTTP API
  - Simulation control (play/pause/speed adjustment)
  - Time manipulation and state navigation
  - Web-based management interface

### 2.2 Device Identification Flow

1. Display device connects to ODIS via WebSocket
2. ODIS identifies the device using configurable rules (device ID, mounting point, etc.)
3. Device receives identification metadata (device type, location, configuration)
4. Device uses this information to determine appropriate layout and behavior
5. ODIS begins streaming relevant onboard data based on device context

### 2.3 Future Extensions

The architecture supports future implementation of additional data sources, particularly:

- **IBIS-IP (Integrated Onboard Information System - Internet Protocol)**
- **File-based data sources**
- **Custom protocol adapters**

---

## 3. Component Descriptions

### 3.1 ODIS Core Server

**Responsibilities:**

- **Device Identification**: Identifies connecting devices and provides configuration metadata
- **Data Coordination**: Manages data flow from sources to connected devices
- **Protocol Handling**: Serves both WebSocket (for displays) and HTTP APIs (for management)
- **State Management**: Maintains current journey state and device connections

**Core Modules:**

1. **Data Coordinator**: Central hub that processes incoming data and manages state
2. **Data Sources**: Pluggable adapters (Baselink, HTTP simulation, future IBIS-IP)
3. **WebSocket Server**: Real-time data distribution to display devices
4. **HTTP API Server**: Management interface and simulation control
5. **Device Configuration Service**: Device identification and rule management

### 3.2 Data Source Implementations

#### Baselink Data Fetcher

- **Purpose**: Connects to external Baselink systems for real vehicle data
- **Interface**: gRPC client connecting to Baselink servers
- **Features**: Vehicle trip polling, data transformation, error handling

#### HTTP Data Fetcher

- **Purpose**: Provides simulation capabilities for development and testing
- **Interface**: Reads from internal simulation state managed via HTTP API
- **Features**: Manual journey control, time manipulation, state navigation

### 3.3 WebDisplay Configuration Server

- **Role:** External server (outside vehicle network) that serves the web application frontend, layouts, and configuration
- **Provides:**
  - Progressive Web App (PWA) frontend code
  - Layout templates and widget configurations
  - Media assets (images, fonts, etc.)
  - Configuration for connecting to ODIS

### 3.4 Browser‑based Display Clients

- **Platform:** Embedded WebView or kiosk browser
- **Startup Flow:**
  1. Load web application from WebDisplay Server
  2. Receive configuration (layouts, widgets, data source URLs)
  3. Connect to ODIS WebSocket using provided URL
  4. Receive device identification from ODIS
  5. Render passenger information in real time using data from ODIS
  6. Optionally maintain WebSocket connection to WebDisplay Server for updates

### 3.5 Web Management Interface

- **Platform:** React + TypeScript web application embedded in ODIS
- **Purpose:** Development, testing, and simulation control
- **Features:**
  - Trip loading from Baselink or manual JSON files
  - Simulation control (play/pause/speed/time manipulation)
  - Real-time state monitoring
  - Journey visualization and debugging tools

---

## 4. Data Flow & Protocols

### 4.1 Unified Onboard Data Model

ODIS transforms data from various sources into a unified onboard data model that includes:

- **Journey Information**: Trip details, stops, schedules, and real-time updates
- **Vehicle Information**: Vehicle ID, coach number, door status
- **Location Status**: Current position relative to stops (BetweenStops, BeforeStop, AtStop, AfterStop)
- **Device Context**: Device identification and configuration metadata

### 4.2 Data Flow Table

| Step | Source                          | Transport           | Payload                          |
|------|---------------------------------|---------------------|----------------------------------|
| 1    | WebDisplay Server → Displays    | HTTPS               | Web app, layouts, configs        |
| 2    | ODIS → Baselink Server          | gRPC                | Trip/vehicle data requests       |
| 3    | Baselink Server → ODIS          | gRPC                | Vehicle & journey status data    |
| 4    | Web UI → ODIS                   | HTTP API            | Simulation control commands      |
| 5    | ODIS → Display Clients          | WebSocket           | Device ID + Real-time data       |
| 6    | Display Clients → ODIS          | WebSocket ping/pong | Heartbeat / health checks        |
| 7    | ODIS → Web UI                   | HTTP API            | Current state and status         |

### 4.3 Configuration Modes

**Baselink Mode:**

```bash
cargo run -- --data-source baselink --baselink-url https://baselink-server:50051 --vehicle-id vehicle-123
```

**HTTP Simulation Mode:**

```bash
cargo run -- --data-source http --vehicle-id test-vehicle-123
```

---

## 5. Device Identification & Configuration

### 5.1 Device Configuration Rules

ODIS uses configurable rules to identify devices and provide appropriate metadata:

- **Device Mounting Point**: Physical location identifier (e.g., "front", "rear", "side")
- **Device Type**: Classification (e.g., "passenger_info", "door_display", "route_map")
- **Coach Context**: Vehicle-specific information (coach number, vehicle ID)
- **Layout Assignment**: Determines which display layout to use

### 5.2 Configuration Management

- **Static Configuration**: JSON-based device identification rules
- **Dynamic Updates**: Runtime configuration changes via HTTP API
- **Rule Evaluation**: Flexible matching based on device properties

---

## 6. HTTP API Endpoints

### 6.1 Baselink Integration

- `GET /api/baselink/status` - Connection status and version
- `GET /api/baselink/vehicles` - Available vehicles
- `GET /api/baselink/trips/{trip_id}` - Trip details

### 6.2 Simulation Control

- `GET /api/simulation/state` - Current simulation state
- `POST /api/simulation/resume` - Start/resume playback
- `POST /api/simulation/stop` - Stop playback
- `POST /api/simulation/time` - Set specific time
- `POST /api/simulation/next-state` - Navigate to next state
- `POST /api/simulation/previous-state` - Navigate to previous state

### 6.3 Data Management

- `POST /api/data/load/journey` - Load journey data
- `GET /api/data/sources` - Available data sources

---

## 7. Deployment & Runtime

### 7.1 Technology Stack

- **Runtime**: Rust with Tokio async runtime
- **WebSocket**: tokio-tungstenite for real-time communication
- **HTTP API**: Axum web framework for REST endpoints
- **Time Handling**: jiff (modern replacement for chrono)
- **Error Handling**: anyhow for error management
- **Data Storage**: In-memory (no persistent storage required)
- **Frontend**: React + TypeScript (embedded in binary via rust-embed)

### 7.2 Data Source Architecture

- **Pluggable Design**: Multiple data sources supported
- **Single Active Source**: Only one data source enabled at runtime
- **Unified Model**: All sources convert to common onboard data model
- **Protocol Agnostic**: Sources can use different protocols (gRPC, HTTP, file, etc.)

### 7.2 Deployment Options

- **Single Binary**: All components embedded in one executable
- **Container**: Docker image with embedded web assets
- **Development**: Separate frontend dev server with API proxy

### 7.3 Monitoring & Logging

- **Structured Logging**: JSON format for production, human-readable for development
- **Health Monitoring**: Built-in health checks and metrics
- **Error Handling**: Graceful degradation and automatic recovery

---

## 8. Future Roadmap

### 8.1 Planned Features

- **IBIS-IP Integration**: Support for IBIS-IP protocol
- **Enhanced Device Rules**: More sophisticated device identification
- **Performance Optimization**: Improved data processing and distribution
- **Advanced Simulation**: More realistic journey simulation capabilities

### 8.2 Integration Opportunities

- **DNS-SD Discovery**: Automatic service discovery for displays
- **Configuration Management**: Centralized configuration distribution
- **Analytics**: Journey and device usage analytics
- **Multi-Vehicle Support**: Support for train consists and multiple vehicles

---

*This document reflects the current implementation as of 2025 and will be updated as the system evolves.*
