# ODIS Development Roadmap

This document outlines planned features and improvements for the Onboard Display Information Server.

## Current Status

### Implemented Features ✅

- **Dual Data Sources**: Baselink (production) and HTTP (simulation) data sources
- **Device Identification**: Configurable device identification and context assignment
- **Web Management Interface**: React-based UI for simulation control and monitoring
- **Real-time Distribution**: WebSocket-based data streaming to connected displays
- **Simulation Control**: Comprehensive playback, time, and navigation controls
- **Journey Management**: Loading from Baselink or manual JSON files

### Core Architecture ✅

- **Pluggable Data Sources**: Extensible architecture for new data source implementations
- **Unified Data Model**: Common onboard data format across all sources
- **Single Binary Deployment**: Embedded web assets and self-contained executable
- **HTTP API**: RESTful endpoints for management and control

## Planned Features

### Phase 1: Enhanced Simulation Capabilities

#### Vehicle Context Enhancement
- **Coach Number Control**: Add coach number configuration to simulation
- **Vehicle ID Management**: Enhanced vehicle identification in simulation mode
- **Multi-Vehicle Support**: Support for train consists with multiple vehicles

#### Journey State Management
- **Off-Trip Mode**: Support for vehicles not currently on a journey
- **Journey Without Location**: Handle journeys that are loaded but not actively served
- **Service States**: Support for different service states (in-service, out-of-service, deadhead)

#### Advanced Simulation Features
- **Delay Simulation**: Realistic delay scenarios and recovery
- **Disruption Handling**: Cancellations, diversions, and service interruptions
- **Real-time Adjustments**: Dynamic journey modifications during simulation

### Phase 2: IBIS-IP Integration

#### Protocol Implementation
- **IBIS-IP Data Source**: Native support for IBIS-IP protocol
- **Service Discovery**: DNS-SD integration for automatic service discovery
- **Protocol Compliance**: Full VDV-301 compliance for IBIS-IP communication

#### Integration Features
- **Automatic Configuration**: Device configuration via IBIS-IP services
- **Multi-Protocol Support**: Simultaneous support for multiple protocols
- **Fallback Mechanisms**: Graceful degradation when services are unavailable

### Phase 3: Advanced Device Management

#### Enhanced Device Rules
- **Complex Matching**: Advanced device identification rules and patterns
- **Dynamic Assignment**: Runtime device reassignment and configuration updates
- **Device Groups**: Logical grouping of devices for bulk operations

#### Configuration Management
- **Centralized Configuration**: Remote configuration management and distribution
- **Version Control**: Configuration versioning and rollback capabilities
- **Validation**: Configuration validation and testing tools

### Phase 4: Analytics and Monitoring

#### Performance Monitoring
- **Connection Analytics**: Device connection patterns and reliability metrics
- **Data Flow Monitoring**: Real-time data distribution monitoring
- **Performance Metrics**: Latency, throughput, and error rate tracking

#### Journey Analytics
- **Usage Patterns**: Analysis of journey data usage and display patterns
- **Performance Analysis**: Journey timing and schedule adherence analysis
- **Reporting**: Automated reporting and dashboard capabilities

### Phase 5: Production Enhancements

#### Scalability Improvements
- **Load Balancing**: Support for multiple server instances
- **Caching**: Intelligent caching for improved performance
- **Resource Optimization**: Memory and CPU usage optimization

#### Reliability Features
- **Health Monitoring**: Comprehensive health checks and monitoring
- **Automatic Recovery**: Self-healing capabilities for common failures
- **Backup Systems**: Data backup and disaster recovery features

## Technical Debt and Improvements

### Code Quality
- **Test Coverage**: Comprehensive unit and integration test coverage
- **Documentation**: API documentation and developer guides
- **Code Organization**: Refactoring for improved maintainability

### Performance Optimization
- **Memory Usage**: Optimize memory usage for long-running deployments
- **Network Efficiency**: Optimize WebSocket and HTTP communication
- **Startup Time**: Reduce application startup time

### Developer Experience
- **Development Tools**: Enhanced debugging and development tools
- **Local Development**: Improved local development setup and workflows
- **CI/CD**: Automated testing and deployment pipelines

## Implementation Priority

### High Priority (Next 3 months)
1. Enhanced vehicle context in simulation (coach number, vehicle ID)
2. Off-trip and journey-without-location modes
3. Improved error handling and edge cases
4. Test coverage improvements

### Medium Priority (3-6 months)
1. IBIS-IP protocol implementation
2. Advanced device identification rules
3. Performance monitoring and analytics
4. Configuration management enhancements

### Low Priority (6+ months)
1. Multi-vehicle and train consist support
2. Advanced analytics and reporting
3. Scalability and load balancing
4. Backup and disaster recovery

## Contributing

### Feature Requests
- Create GitHub issues for new feature requests
- Provide detailed use cases and requirements
- Consider implementation complexity and priority

### Development Guidelines
- Follow existing code patterns and architecture
- Ensure backward compatibility where possible
- Include comprehensive tests for new features
- Update documentation for user-facing changes

---

This roadmap is a living document and will be updated as priorities and requirements evolve.
