# Frontend Web UI Specification

## Project Overview

This document specifies the implementation of a React + TypeScript web frontend for the Onboard Display Information Server (ODIS). The web UI provides an intuitive, modern interface for testing, simulation, and management of onboard journey data.

**Project Location:** `ui/` directory within the main project

## Architecture Overview

### Technology Stack

- **Frontend Framework:** React 18 with TypeScript
- **Build Tool:** Vite (fast development and optimized production builds)
- **UI Framework:** Material-UI (MUI) v5 - mature, well-documented, accessible
- **State Management:** Zustand (lightweight, TypeScript-friendly)
- **HTTP Client:** Axios with TypeScript support
- **WebSocket:** Native WebSocket API with automatic reconnection
- **Styling:** MUI's emotion-based styling system
- **Development:** ESLint, Prettier, TypeScript strict mode

### Project Structure

```text
ui/
├── package.json
├── vite.config.ts
├── tsconfig.json
├── index.html
├── src/
│   ├── App.tsx                     # Main app component with routing
│   ├── main.tsx                    # Entry point
│   ├── components/
│   │   ├── Layout/
│   │   │   ├── AppLayout.tsx       # Main layout with sidebar
│   │   │   ├── Sidebar.tsx         # Left navigation menu
│   │   │   └── Header.tsx          # Top header bar
│   │   ├── TripLoading/
│   │   │   ├── TripLoadingPage.tsx # Main trip loading page
│   │   │   ├── VehicleSelector.tsx # Vehicle-based selection
│   │   │   ├── TripIdInput.tsx     # Direct trip ID input
│   │   │   ├── TripPreview.tsx     # Trip preview component
│   │   │   └── ConnectionStatus.tsx# Baselink status display
│   │   ├── Simulation/
│   │   │   ├── SimulationPage.tsx  # Main simulation dashboard
│   │   │   ├── PlaybackControls.tsx# Play/pause/speed controls
│   │   │   ├── TimeControls.tsx    # Time navigation
│   │   │   ├── NavigationControls.tsx # State/stop jumping
│   │   │   ├── SimulationState.tsx # Current state display
│   │   │   ├── JourneyProgress.tsx # Journey overview
│   │   │   └── StopTimeline.tsx    # Stop timeline display
│   │   └── Common/
│   │       ├── LoadingSpinner.tsx  # Loading states
│   │       ├── ErrorDisplay.tsx    # Error handling
│   │       └── Toast.tsx           # Notifications
│   ├── hooks/
│   │   ├── useApi.ts               # HTTP API integration
│   │   ├── useWebSocket.ts         # WebSocket connection
│   │   ├── useSimulation.ts        # Simulation state management
│   │   └── useBaselink.ts          # Baselink operations
│   ├── services/
│   │   ├── api.ts                  # HTTP API client
│   │   ├── websocket.ts            # WebSocket service
│   │   └── types.ts                # API type definitions
│   ├── store/
│   │   ├── simulationStore.ts      # Simulation state
│   │   ├── baselinkStore.ts        # Baselink data
│   │   └── uiStore.ts              # UI state
│   ├── utils/
│   │   ├── formatters.ts           # Data formatting
│   │   └── constants.ts            # App constants
│   └── theme.ts                    # MUI theme configuration
└── dist/                           # Build output
```

## User Interface Design

### Layout Structure

#### Main Layout (`AppLayout.tsx`)

- **Left Sidebar (240px wide):** Primary navigation with two main sections
- **Main Content Area:** Dynamic content based on selected navigation item
- **Header Bar:** Application title, connection status, and user actions

#### Sidebar Navigation (`Sidebar.tsx`)

Two main navigation sections:

1. **Trip Loading** 🚌
   - **Vehicle Selection** - Select vehicle to load its current trip
   - **Trip ID Input** - Enter trip ID directly to fetch trip data

2. **Simulation** ▶️
   - **Simulation Control & State View** - Complete simulation dashboard

### Page Specifications

#### 1. Trip Loading Page (`TripLoadingPage.tsx`)

**Layout:** Two-tab interface for different loading methods

##### Tab 1: Vehicle Selection

**Visual Layout:**

```text
┌─ Connection Status ─────────────────────────────────┐
│ 🟢 Connected to Baselink | Version 1.2.3           │
│ ↻ Refresh Connection                                │
└─────────────────────────────────────────────────────┘

┌─ Vehicle List ──────────────────────────────────────┐
│ Vehicle ID    │ Trip ID      │ Status              │
│ vehicle_123   │ Bus 123      │ Active              │
│ vehicle_456   │ Bus 456      │ Active              │
│ [↻ Refresh List]                                   │
└─────────────────────────────────────────────────────┘

┌─ Trip Preview ──────────────────────────────────────┐
│ Trip ID: 422250752                                  │
│ Line: 3 → Altwarmbüchen                           │
│ Stops: 15 total                                     │
│ [Load Trip into Simulation]                        │
└─────────────────────────────────────────────────────┘
```

**Components:**

- **ConnectionStatus:** Shows baselink connection state, version info, reconnect option
- **VehicleSelector:** Data table with vehicle list, selection handling, refresh functionality
- **TripPreview:** Trip details display with load action

##### Tab 2: Trip ID Input

**Visual Layout:**

```text
┌─ Trip ID Input ─────────────────────────────────────┐
│ Trip ID: [_________________] [Get Trip]             │
│                                                     │
│ ┌─ Trip Preview ────────────────────────────────────┐
│ │ Trip ID: 422250752                               │
│ │ Line: 3 → Altwarmbüchen                        │
│ │ Stops: 15 total                                  │
│ │ [Load Trip into Simulation]                     │
│ └──────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────┘
```

**Components:**

- **TripIdInput:** Simple form with text input and fetch button
- **TripPreview:** Reused component from vehicle selection tab

**Error Handling:**

- Toast notifications for API errors
- Inline validation messages
- Graceful handling of baselink connection issues

#### 2. Simulation Control & State Page (`SimulationPage.tsx`)

**Layout:** Multi-panel dashboard organized in grid layout

**Visual Layout:**

```text
┌─ Playback Controls ──────┬─ Time Controls ──────────┐
│ Status: ⏸️ Stopped       │ Current Time:            │
│ Speed: [4x ▼]            │ 14:32:15                 │
│ [▶️ Resume] [⏸️ Stop]    │ Set Time: [HH:MM:SS] [⚡] │
└──────────────────────────┴───────────────────────────┘

┌─ Navigation Controls ────┬─ Journey Progress ───────┐
│ States: [◀️] 3/8 [▶️]     │ Trip: Bus 123           │
│ Stops:  [◀️] 5/15 [▶️]    │ Line 3 → Altwarmbüchen │
│                          │ ████████▓▓▓▓▓▓▓ 53%     │
└──────────────────────────┴───────────────────────────┘

┌─ Current State ──────────┬─ Stop Timeline ──────────┐
│ 📍 Wettbergen (Platform SB2) │ 🔵 Hauptbahnhof      │
│ Status: At Stop          │ ⚪ Steintor             │
│ Arrival: 14:30 (on time) │ 🟡 Wettbergen (current) │
│ Next: Steintor in 3 mins │ ⚪ Altwarmbüchen       │
└──────────────────────────┴───────────────────────────┘
```

**Panel Components:**

1. **PlaybackControls:**
   - Current playback state indicator (Playing/Stopped)
   - Speed multiplier dropdown (1x-10x)
   - Resume/Stop buttons with visual feedback

2. **TimeControls:**
   - Large current timestamp display
   - Manual time input with set button
   - Independent of playback state

3. **NavigationControls:**
   - State navigation (Previous/Next State with counter)
   - Stop navigation (Previous/Next Stop with counter)
   - Clear visual indicators of current position

4. **JourneyProgress:**
   - Trip metadata (ID, line, destination)
   - Progress bar showing journey completion
   - High-level status information

5. **SimulationState:**
   - Current stop name and platform
   - Location status (approaching, at stop, departed)
   - Timing information (scheduled vs actual)
   - Next stop preview

6. **StopTimeline:**
   - Scrollable list of all stops
   - Current stop highlighted
   - Status indicators for each stop
   - Time information where available

**Real-time Updates:**

- WebSocket connection status indicator
- Auto-refresh simulation state every 2 seconds
- Visual feedback for all state changes

## State Management

### Zustand Store Structure

#### Simulation Store (`simulationStore.ts`)
```typescript
interface SimulationState {
  // Current simulation state
  journey: Journey | null;
  currentCallIndex: number | null;
  status: LocationStatus | null;
  timestamp: number;
  playbackState: 'playing' | 'stopped';
  speedMultiplier: number;
  
  // WebSocket connection
  isConnected: boolean;
  connectionError: string | null;
  
  // Actions
  updateState: (state: SimulationStateResponse) => void;
  setPlaybackState: (state: 'playing' | 'stopped') => void;
  setSpeedMultiplier: (speed: number) => void;
  setTimestamp: (timestamp: number) => void;
}
```

#### Baselink Store (`baselinkStore.ts`)
```typescript
interface BaselinkState {
  // Connection status
  connectionStatus: BaselinkConnectionStatus;
  isConnected: boolean;
  
  // Vehicles and trips
  vehicles: VehicleInfo[];
  selectedVehicle: VehicleInfo | null;
  selectedTrip: Journey | null;
  
  // Loading states
  vehiclesLoading: boolean;
  tripLoading: boolean;
  
  // Actions
  setVehicles: (vehicles: VehicleInfo[]) => void;
  selectVehicle: (vehicle: VehicleInfo) => void;
  setSelectedTrip: (trip: Journey) => void;
  setConnectionStatus: (status: BaselinkConnectionStatus) => void;
}
```

#### App Store (`appStore.ts`)
```typescript
interface AppState {
  // UI state
  currentPage: 'baselink' | 'simulation';
  sidebarCollapsed: boolean;
  
  // Notifications
  toasts: Toast[];
  
  // Actions
  setCurrentPage: (page: string) => void;
  toggleSidebar: () => void;
  addToast: (toast: Toast) => void;
  removeToast: (id: string) => void;
}
```

## API Integration

### HTTP API Endpoints (Based on Current Implementation)

The frontend integrates with the following HTTP API endpoints:

**Baselink Domain:**

- `GET /api/baselink/status` - Connection status and version
- `GET /api/baselink/vehicles` - List available vehicles
- `GET /api/baselink/trips/{trip_id}` - Get trip by ID

**Simulation Domain:**

- `GET /api/simulation/state` - Get complete simulation state
- `POST /api/simulation/stop` - Stop playback
- `POST /api/simulation/resume` - Resume with optional speed
- `POST /api/simulation/time` - Set explicit timestamp
- `POST /api/simulation/next-state` - Jump to next state
- `POST /api/simulation/previous-state` - Jump to previous state
- `POST /api/simulation/next-stop` - Jump to next stop
- `POST /api/simulation/previous-stop` - Jump to previous stop

**Data Domain:**

- `POST /api/data/load/journey` - Load journey into simulation
- `GET /api/data/sources` - Get available data sources

### HTTP API Service (`api.ts`)

```typescript
class ApiService {
  private baseURL = '/api';

  // Baselink endpoints
  async getBaselinkStatus(): Promise<BaselinkStatus>;
  async getVehicles(): Promise<VehicleInfo[]>;
  async getTripById(tripId: string): Promise<Journey>;

  // Simulation endpoints
  async getSimulationState(): Promise<SimulationState>;
  async stopPlayback(): Promise<void>;
  async resumePlayback(speedMultiplier?: number): Promise<void>;
  async setTime(timestamp: number): Promise<void>;
  async nextState(): Promise<void>;
  async previousState(): Promise<void>;
  async nextStop(): Promise<void>;
  async previousStop(): Promise<void>;

  // Data loading endpoints
  async loadJourney(journey: Journey): Promise<void>;
}
```

### WebSocket Integration (`websocket.ts`)

```typescript
class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(onMessage: (data: any) => void): void;
  disconnect(): void;
  private reconnect(): void;

  // Event handlers
  onOpen: () => void;
  onClose: () => void;
  onError: (error: Event) => void;
  onMessage: (data: any) => void;
}
```

## Component Specifications

### Shared Components

#### LoadingSpinner (`LoadingSpinner.tsx`)
- MUI CircularProgress with overlay
- Configurable size and message
- Used throughout the application for async operations

#### ErrorBoundary (`ErrorBoundary.tsx`)
- React error boundary with fallback UI
- Error reporting and retry functionality
- User-friendly error messages

#### Toast Notifications (`Toast.tsx`)
- MUI Snackbar-based notifications
- Success, error, warning, and info variants
- Auto-dismiss with configurable duration
- Queue management for multiple toasts

#### ConfirmDialog (`ConfirmDialog.tsx`)
- MUI Dialog for user confirmations
- Used for destructive actions
- Customizable title, message, and button labels

### Styling and Theming

#### MUI Theme Configuration (`theme.ts`)
```typescript
const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2', // Professional blue
    },
    secondary: {
      main: '#dc004e', // Accent red
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h5: {
      fontWeight: 600,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none', // Keep normal case
        },
      },
    },
  },
});
```

#### Responsive Design
- Mobile-first approach with MUI breakpoints
- Sidebar collapses to drawer on mobile
- Touch-friendly button sizes
- Readable font sizes across devices

## Development Setup

### Package Configuration (`package.json`)

```json
{
  "name": "odis-web-ui",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint src --ext ts,tsx --fix",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "react": "^19.1.0",
    "react-dom": "^19.1.0",
    "react-router-dom": "^7.6.2",
    "@mui/material": "^7.1.2",
    "@mui/icons-material": "^7.1.2",
    "@emotion/react": "^11.14.0",
    "@emotion/styled": "^11.14.0",
    "axios": "^1.10.0",
    "zustand": "^5.0.5"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@typescript-eslint/eslint-plugin": "^6.0.0",
    "@typescript-eslint/parser": "^6.0.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.45.0",
    "eslint-plugin-react-hooks": "^4.6.0",
    "eslint-plugin-react-refresh": "^0.4.0",
    "typescript": "^5.0.2",
    "vite": "^4.4.0"
  }
}
```

### Vite Configuration (`vite.config.ts`)

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:8080',
        ws: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'esbuild',
  },
})
```

### TypeScript Configuration (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## Development Workflow

### Local Development
1. **Start Backend:** `cargo run` (from project root)
2. **Start Frontend:** `cd ui && npm run dev`
3. **Access Application:** http://localhost:3000
4. **API Proxy:** Automatic proxy to backend at localhost:8080

### Build Process
1. **Type Check:** `npm run type-check`
2. **Lint:** `npm run lint`
3. **Build:** `npm run build`
4. **Output:** Static files in `ui/dist/` ready for embedding

### Code Quality
- **ESLint:** Enforces code style and catches common errors
- **TypeScript:** Strict mode for type safety
- **Prettier:** Code formatting (integrated with ESLint)
- **Git Hooks:** Pre-commit linting and type checking

## Error Handling and UX

### Error States
- **Network Errors:** Toast notifications with retry options
- **Validation Errors:** Inline form validation messages
- **API Errors:** User-friendly error messages with error codes
- **Connection Loss:** Persistent notification with reconnect attempts

### Loading States
- **Skeleton Loaders:** For data-heavy components
- **Button Loading:** Spinner in buttons during async operations
- **Page Loading:** Full-page loading for initial data
- **Progressive Loading:** Load non-critical data after initial render

### User Feedback
- **Toast Notifications:** Success, error, warning, info messages
- **Visual State Changes:** Button states, active selections
- **Progress Indicators:** For long-running operations
- **Confirmation Dialogs:** For destructive actions

## Future Enhancements

### Phase 1 Extensions
- **Export Functionality:** Download current simulation state
- **Advanced Time Controls:** Date picker, time range selection
- **Trip History:** Recently loaded trips list
- **Keyboard Shortcuts:** Power user navigation

### Phase 2 Features
- **Map Visualization:** Route display with stop markers
- **Real-time Metrics:** Performance monitoring dashboard
- **Multi-trip Comparison:** Side-by-side trip analysis
- **Custom Themes:** Light/dark mode toggle

### Integration Features
- **File Upload:** Drag-and-drop trip data files
- **Trip Templates:** Save and reuse common trip configurations
- **Advanced Filtering:** Search and filter trips by multiple criteria
- **Collaboration:** Share trip states via URLs

## Production Deployment

### Asset Embedding
The built frontend will be embedded in the Rust binary using `rust-embed`:

```rust
#[derive(RustEmbed)]
#[folder = "ui/dist/"]
struct WebAssets;

// Serve index.html for SPA routes
// Serve static assets with proper MIME types
// Handle 404s gracefully
```

### Single Binary Deployment

- Frontend assets embedded in Rust binary
- No external dependencies for web UI
- Automatic fallback to index.html for SPA routing
- Production-optimized builds with compression

### Implementation Priority

#### Phase 1: Foundation

1. Setup Vite + React + TypeScript project
2. Basic layout with sidebar navigation
3. API service layer and error handling
4. Basic routing between two main pages

#### Phase 2: Trip Loading

1. Connection status display
2. Vehicle selection interface
3. Trip ID input interface
4. Trip preview component
5. Integration with baselink API endpoints

#### Phase 3: Simulation Control

1. Simulation state display
2. Playback controls implementation
3. Time controls and navigation
4. Journey progress visualization
5. Real-time WebSocket updates

#### Phase 4: Polish & Production

1. Error handling and edge cases
2. Loading states and UX improvements
3. Build optimization for embedding
4. Documentation and testing

---

This specification provides a comprehensive roadmap for implementing a modern, user-friendly web interface for ODIS management and simulation control, with significant improvements in usability and visual feedback.