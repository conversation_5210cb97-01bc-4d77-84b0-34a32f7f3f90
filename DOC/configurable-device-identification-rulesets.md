# Configurable Device Identification Rule Sets

## Overview

This specification extends the Onboard Display Protocol with runtime reconfiguration capabilities for device identification rules. The design prioritizes simplicity and minimal architectural changes while providing essential configuration management functionality.

**Design Principles:**

- **Minimal Code Entities**: Leverage existing architecture with minimal new components
- **Simple Operations**: Straightforward configuration reload and re-identification process
- **Reliable**: Atomic updates with graceful error handling
- **Backward Compatible**: Existing protocol behavior preserved

## Architecture Changes

The solution requires minimal modifications to the existing layered architecture:

```txt
┌─────────────────────────────────────────────────────────────┐
│                    Core Application                         │
│  ┌─────────────────────────────────────────────────────────┤
│  │             Data Broadcasting Layer                     │
│  │  ┌─────────────────┬─────────────────────────────────────┤
│  │  │DataBroadcastMgr │    +ConfigReloadCoordinator        │ ← NEW
│  │  └─────────────────┴─────────────────────────────────────┤
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                    Protocol Layer                           │
│  ┌─────────────────────────────────────────────────────────┤
│  │           OnboardDisplayProtocol                        │
│  │  ┌─────────────────┬─────────────────┬─────────────────┤
│  │  │AwaitDeviceHello │IdentifyingDevice│ StreamingData   │ ← ENHANCED
│  │  │    State        │     State       │ +ReIdentifying  │
│  │  └─────────────────┴─────────────────┴─────────────────┤
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│              Device Identification Layer                    │
│  ┌─────────────────────────────────────────────────────────┤
│  │           DeviceIdentificationService                   │ ← ENHANCED
│  │  ┌─────────────────┬─────────────────────────────────────┤
│  │  │  +ConfigReload  │    +FileWatcher (optional)         │ ← NEW
│  │  └─────────────────┴─────────────────────────────────────┤
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│              HTTP Management API (NEW)                     │ ← NEW
│  ┌─────────────────────────────────────────────────────────┤
│  │           /admin/reload-device-config                   │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                 Connection Layer                            │
│  ┌─────────────────────────────────────────────────────────┤
│  │             WebSocketConnection                         │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Component Modifications

### 1. DeviceIdentificationService (Enhanced)

**New Capabilities:**

```rust
struct DeviceIdentificationService {
    device_config: Arc<RwLock<DeviceConfig>>, // Changed from direct DeviceConfig
    config_file_path: PathBuf,
    file_watcher: Option<FileWatcher>, // Optional file watching
}

impl DeviceIdentificationService {
    // Existing methods unchanged
    async fn identify_device(&self, connection_info: &ConnectionInfo) 
        -> Result<OnboardDeviceIdentification, IdentificationError>;

    // New methods
    async fn reload_configuration(&self) -> Result<(), ConfigurationError>;
    async fn validate_configuration_file(&self, path: &Path) -> Result<DeviceConfig, ValidationError>;
    async fn get_current_configuration(&self) -> DeviceConfig;
}
```

**Implementation Notes:**

- Configuration stored in `Arc<RwLock<DeviceConfig>>` for atomic updates
- File watching is optional - can be enabled via configuration flag
- Validation occurs before configuration replacement
- Failed reloads preserve existing configuration

### 2. OnboardDisplayProtocol (Enhanced)

**State Machine Enhancement:**

```rust
enum ProtocolState {
    AwaitDeviceHello {
        start_time: Instant,
        timeout_duration: Duration,
    },
    IdentifyingDevice {
        start_time: Instant,
        identification_service: Arc<DeviceIdentificationService>,
        hello_data: Option<HelloMessage>,
    },
    StreamingData {
        device_info: OnboardDeviceIdentification,
        last_data_sent: Option<Instant>,
        config_reload_receiver: Option<broadcast::Receiver<()>>, // NEW
    },
}
```

**Enhanced State Transitions:**

```txt
  ┌─────────────────┐
  │AwaitDeviceHello │
  └─────────┬───────┘
            │ connection established
            │ wait 3s for optional Hello
            ▼
  ┌─────────────────┐
  │IdentifyingDevice│◄──┐ retry on failure
  └─────────┬───────┘   │
            │ device identified
            ▼           │ config reload signal
  ┌─────────────────┐   │
  │ StreamingData   │───┘
  └─────────────────┘
            │
            ▼ connection closed
  ┌─────────────────┐
  │   Terminated    │
  └─────────────────┘
```

**Re-identification Process:**

1. Receive configuration reload signal in `StreamingData` state
2. Transition back to `IdentifyingDevice` state (reusing existing logic)
3. Re-identify device with new configuration
4. Send updated device-identification message to client
5. Return to `StreamingData` state with new device information

### 3. DataBroadcastManager (Enhanced)

**Configuration Reload Coordination:**

```rust
struct DataBroadcastManager {
    active_protocols: HashMap<ConnectionId, UnboundedSender<OnboardData>>,
    last_data: Arc<Mutex<Option<OnboardData>>>,
    config_reload_broadcaster: broadcast::Sender<()>, // NEW
}

impl DataBroadcastManager {
    // Existing methods unchanged
    
    // New method
    async fn trigger_configuration_reload(&self) {
        let _ = self.config_reload_broadcaster.send(());
        info!("Configuration reload signal sent to {} active connections", 
              self.active_protocols.len());
    }
}
```

### 4. HTTP Management API (New)

**Simple Configuration Management Endpoint:**

```rust
async fn reload_device_configuration(
    device_identification_service: Arc<DeviceIdentificationService>,
    data_broadcast_manager: Arc<DataBroadcastManager>,
) -> Result<Json<ReloadResponse>, StatusCode> {
    
    match device_identification_service.reload_configuration().await {
        Ok(()) => {
            data_broadcast_manager.trigger_configuration_reload().await;
            Ok(Json(ReloadResponse {
                success: true,
                message: "Device configuration reloaded successfully".to_string(),
                timestamp: Utc::now(),
            }))
        }
        Err(e) => {
            error!("Configuration reload failed: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}
```

**API Specification:**

```txt
POST /admin/reload-device-config
Content-Type: application/json

Response 200:
{
    "success": true,
    "message": "Device configuration reloaded successfully",
    "timestamp": "2024-01-15T10:30:00Z"
}

Response 500:
{
    "success": false,
    "message": "Configuration validation failed: Invalid JSON syntax at line 15",
    "timestamp": "2024-01-15T10:30:00Z"
}
```

## Configuration File Handling

### File Format (Unchanged)

The existing `devices.jsonc` format remains unchanged:

```jsonc
[
    {
        "lookup": {
            "ip": "*************",
            "dns": "door-display-left-1", 
            "displayId": "display_001"
        },
        "deviceId": "door-left-display",
        "deviceClassifier": "onboard-display-information"
    }
]
```

### Validation Rules

Configuration validation ensures:

1. **JSON Syntax**: Valid JSONC format
2. **Schema Compliance**: All required fields present
3. **Logical Consistency**: No duplicate device entries
4. **Network Validity**: IP addresses are valid format
5. **Field Constraints**: Non-empty strings for required fields

### Atomic Updates

Configuration updates are atomic:

1. Load and parse new configuration file
2. Validate entire configuration
3. If validation passes, atomically replace current configuration
4. If validation fails, keep existing configuration unchanged
5. Log success/failure with detailed error messages

## Protocol Message Flow

### Initial Connection (Unchanged)

The initial connection flow remains exactly as specified in the original protocol.

### Configuration Reload Flow

```txt
Admin Client                 Server                     Display Clients
     │                        │                             │
     │── POST /admin/reload ──→│                             │
     │                        │                             │
     │                        │── Load & Validate Config ──┤
     │                        │                             │
     │                        │── Broadcast Reload Signal ─→│
     │←── 200 OK Response ────│                             │
     │                        │                             │
     │                        │                       ┌─────┴─────┐
     │                        │                       │Protocol   │
     │                        │                       │Transitions│
     │                        │                       │to         │
     │                        │                       │Identifying│
     │                        │                       │Device     │
     │                        │                       └─────┬─────┘
     │                        │                             │
     │                        │←── Re-identification ──────│
     │                        │                             │
     │                        │── Device Identification ───→│
     │                        │                             │
     │                        │── Resume Data Streaming ───→│
```

## Error Handling

### Configuration Errors

```rust
#[derive(Debug, thiserror::Error)]
enum ConfigurationError {
    #[error("Configuration file not found: {path}")]
    FileNotFound { path: String },
    
    #[error("Invalid JSON syntax: {message}")]
    InvalidJson { message: String },
    
    #[error("Schema validation failed: {field}")]
    SchemaValidation { field: String },
    
    #[error("Logical validation failed: {message}")]
    LogicalValidation { message: String },
    
    #[error("IO error: {message}")]
    IoError { message: String },
}
```

### Recovery Strategies

1. **Configuration Load Failure**: Keep existing configuration, log error, return 500 status
2. **Re-identification Failure**: Keep existing device information, log warning, continue operation
3. **Broadcast Failure**: Log error but don't fail the configuration reload
4. **Partial Re-identification Failures**: Individual connections fail gracefully, others continue

## Implementation Checklist

### Phase 1: Core Configuration Reload

- [ ] Modify `DeviceIdentificationService` to use `Arc<RwLock<DeviceConfig>>`
- [ ] Implement `reload_configuration()` method with validation
- [ ] Add configuration file validation logic
- [ ] Add error types and handling

### Phase 2: Protocol Re-identification

- [ ] Add configuration reload signal receiver to `StreamingData` state
- [ ] Implement state transition back to `IdentifyingDevice` on reload signal
- [ ] Ensure updated device-identification message is sent after re-identification
- [ ] Test re-identification flow with various scenarios

### Phase 3: Coordination and API

- [ ] Add configuration reload broadcaster to `DataBroadcastManager`
- [ ] Implement HTTP management endpoint
- [ ] Add proper error responses and logging
- [ ] Integrate reload trigger with broadcast mechanism

### Phase 4: Optional Enhancements

- [ ] Add file system watching capability (optional)
- [ ] Add configuration change statistics/metrics
- [ ] Add configuration backup and rollback capabilities
- [ ] Add configuration validation dry-run endpoint

## Testing Strategy

### Unit Tests

- Configuration validation with various invalid inputs
- Atomic configuration replacement scenarios
- Re-identification logic with different device configurations
- Error handling paths

### Integration Tests

- End-to-end configuration reload with active connections
- Multiple concurrent configuration reloads
- Re-identification with connection failures
- HTTP API error scenarios

### Performance Tests

- Configuration reload with many active connections
- Large configuration file loading
- Memory usage during configuration operations

## Deployment Considerations

### Configuration Management

- Configuration files should be deployed atomically (rename operation)
- Backup current configuration before applying changes
- Consider configuration version management for rollback scenarios

### Monitoring

- Log all configuration reload attempts with timestamps
- Monitor re-identification success/failure rates
- Track configuration reload API usage
- Alert on configuration validation failures

### Security

- Restrict access to configuration reload endpoint (admin only)
- Validate configuration file permissions
- Log all configuration changes for audit purposes
- Consider rate limiting for reload endpoint

## Future Extensions

### Configuration API Enhancements

- GET endpoint to retrieve current device configuration
- Configuration diff endpoint to preview changes
- Configuration history and rollback capabilities

### Advanced Features

- Conditional device rules based on time or other factors
- Configuration templates and inheritance
- Multi-environment configuration management
- Integration with external configuration management systems

This specification provides the essential configuration reload functionality with minimal architectural complexity while maintaining the clean design principles of the original Onboard Display Protocol. 