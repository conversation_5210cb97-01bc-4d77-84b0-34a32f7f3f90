# Onboard Display Protocol

## Overview

The Onboard Display Protocol provides a WebSocket-based communication channel between the Onboard Display Server and display devices. The protocol is designed to be simple, efficient, and reliable for real-time passenger information displays.

**Key Characteristics:**

- WebSocket-based communication
- Flexible device identification (IP-based with optional Hello message enhancement)
- Stateful protocol with clear lifecycle management
- Layered architecture for separation of concerns
- Real-time data updates to connected displays

## Architecture Overview

The protocol implementation follows a layered architecture pattern:

```txt
┌─────────────────────────────────────────────────────────────┐
│                    Core Application                         │
│  ┌─────────────────────────────────────────────────────────┤
│  │             Data Broadcasting Layer                     │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                    Protocol Layer                           │
│  ┌─────────────────────────────────────────────────────────┤
│  │           OnboardDisplayProtocol                        │
│  │  ┌─────────────────┬─────────────────┬─────────────────┤
│  │  │AwaitDeviceHello │IdentifyingDevice│ StreamingData   │
│  │  │    State        │     State       │     State       │
│  │  └─────────────────┴─────────────────┴─────────────────┤
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│              Device Identification Layer                    │
│  ┌─────────────────────────────────────────────────────────┤
│  │           DeviceIdentificationService                   │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                 Connection Layer                            │
│  ┌─────────────────────────────────────────────────────────┤
│  │             WebSocketConnection                         │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Layer Details

### 1. Connection Layer

**Responsibility:** Manages low-level WebSocket connections

```rust
struct WebSocketConnection {
    socket_addr: SocketAddr,
    ws_stream: WebSocketStream<TcpStream>,
    send_channel: UnboundedSender<Message>,
}
```

**Operations:**

- Accept incoming WebSocket connections
- Handle WebSocket handshake
- Manage connection lifecycle (open/close)
- Forward messages between transport and protocol layers
- Connection health monitoring

**Receiving Side Handling:**
Since the protocol is primarily server-to-client (no client messages expected), the receiving side is handled efficiently without spawning dedicated receive tasks:

1. **Integrated with Send Loop**: Use `tokio::select!` to handle both sending and minimal receiving in the same task
2. **Control Frame Processing**: Handle WebSocket control frames (ping, pong, close) as required by the WebSocket specification
3. **Connection Health Detection**: Monitor for connection closure and errors through receive path
4. **Minimal Resource Usage**: Avoid spawning tasks solely for ignoring messages

```rust
// Example of efficient receive handling without dedicated tasks
tokio::select! {
    // Handle outgoing messages
    msg = rx.recv() => {
        if let Some(message) = msg {
            if outgoing.send(message).await.is_err() {
                break; // Connection closed
            }
        }
    }
    // Handle minimal incoming processing (control frames, connection health)
    result = incoming.next() => {
        match result {
            Some(Ok(msg)) if msg.is_close() => break, // Graceful close
            Some(Err(_)) => break, // Connection error
            Some(Ok(msg)) if msg.is_ping() => {
                // Respond to ping with pong (WebSocket requirement)
                let _ = outgoing.send(Message::Pong(msg.into_data())).await;
            }
            _ => {} // Ignore other messages efficiently
        }
    }
}
```

### 2. Protocol Layer

**Responsibility:** Implements the stateful communication protocol

```rust
struct OnboardDisplayProtocol {
    connection_id: ConnectionId,
    state: ProtocolState,
    device_info: Option<OnboardDeviceIdentification>,
    data_sender: UnboundedSender<OnboardData>,
}

enum ProtocolState {
    AwaitDeviceHello {
        start_time: Instant,
        timeout_duration: Duration, // 3 seconds
    },
    IdentifyingDevice {
        start_time: Instant,
        identification_service: Arc<DeviceIdentificationService>,
        hello_data: Option<HelloMessage>,
    },
    StreamingData {
        device_info: OnboardDeviceIdentification,
        last_data_sent: Option<Instant>,
    },
}
```

**State Machine:**

```txt
  ┌─────────────────┐
  │AwaitDeviceHello │
  └─────────┬───────┘
            │ connection established
            │ wait 3s for optional Hello
            │ (Hello received OR timeout)
            ▼
  ┌─────────────────┐
  │IdentifyingDevice│◄──┐ retry on failure
  └─────────┬───────┘   │ (configurable attempts)
            │ device identified ┌─────┐
            ▼           │ timeout/failure
  ┌─────────────────┐   │ │
  │ StreamingData   │   │ ▼
  └─────────────────┘   │ close connection
            │           │
            ▼           │
  ┌─────────────────┐   │
  │   Terminated    │◄──┘
  └─────────────────┘
```

**Protocol Operations:**

- Manage state transitions (AwaitDeviceHello → IdentifyingDevice → StreamingData)
- Handle Hello message timeout (3 seconds) and processing
- Process optional Hello and Bye messages from clients
- Coordinate with device identification service (IP + optional displayId)
- Handle error conditions and recovery

### 3. Device Identification Layer

**Responsibility:** Identifies connected devices and provides device metadata

```rust
struct DeviceIdentificationService {
    device_config: DeviceConfig,
}

struct DeviceConfig {
    devices: Vec<DeviceEntry>,
}

struct DeviceEntry {
    lookup: DeviceLookup,
    deviceId: String,
    deviceClassifier: String,
}

struct DeviceLookup {
    ip: Option<IpAddr>,
    dns: Option<String>,
    displayId: Option<String>,
}
```

**Identification Process:**

1. Extract connection metadata (IP address)
2. If Hello message received, extract displayId from Hello message
3. Match against configured device entries in `devices.jsonc` using AND operation:
   - All non-None fields in DeviceLookup must match gathered metadata (ip, displayId)
   - No precedence between fields - all specified fields must match
4. Return `OnboardDeviceIdentification` if match found
5. Return identification error if no match or ambiguous match

*Note: hostname-based identification is optional and not implemented initially*

```rust
impl DeviceIdentificationService {
    async fn identify_device(&self, connection_info: &ConnectionInfo) 
        -> Result<OnboardDeviceIdentification, IdentificationError>;
}
```

### 4. Data Broadcasting Layer

**Responsibility:** Manages data distribution to connected displays

```rust
struct DataBroadcastManager {
    active_protocols: HashMap<ConnectionId, UnboundedSender<OnboardData>>,
    last_data: Arc<Mutex<Option<OnboardData>>>,
}
```

**Operations:**

- Receive `OnboardData` updates from core application
- Broadcast data to all connected displays in Normal state
- Store last known data for immediate delivery to new connections
- Handle connection lifecycle (add/remove protocols)

## Message Types

**Message Format Rules:**

- Messages shall be simple in structure
- Messages shall use camelCase for field names
- All messages have a `type` field indicating message type

### 1. Server to Client Messages

#### OnboardData Update

```json
{
    "type": "onboard-data",
    "onboard": {
        // OnboardData structure
        "currentTime": "2024-01-15T10:30:00Z",
        "vehicleInfo": { ... },
        "journeyInfo": { ... },
        "stopInfo": { ... }
    }
}
```

#### Device Identification

```json
{
    "type": "device-identification", 
    "deviceId": "*************",
    "deviceClassifier": "onboard-display-information",
}
```

#### Error Message

```json
{
    "type": "error",
    "code": "DEVICE_NOT_IDENTIFIED",
    "message": "Device could not be identified based on connection parameters"
}
```

### 2. Client to Server Messages

#### Hello Message (Optional)

```json
{
    "type": "hello",
    "displayId": "display_001",
    "someMetadata1": "additional_info"
}
```

- Sent within 3 seconds of connection establishment
- `displayId` is mandatory if Hello message is sent
- Enhances device identification beyond IP-based matching
- Server waits maximum 3 seconds, then proceeds with identification

#### Bye Message (Optional)

```json
{
    "type": "bye",
    "reason": "changing configuration"
}
```

- Can be sent before disconnection to provide disconnect reason
- Server logs the reason in connection metadata

**Note:** The protocol works without any client messages - devices can be identified purely by IP address. Client messages enhance but don't replace the core identification mechanism.

## Connection Lifecycle

### 1. Connection Establishment

```txt
Client                                  Server
  │                                       │
  │─────── WebSocket Handshake ─────────→│
  │←─────── Handshake Response ──────────│
  │                                       │
  │        [AwaitDeviceHello State]       │
  │        [Server waits 3s for Hello]    │
  │                                       │
  │──── Hello Message (Optional) ───────→│
  │                                       │
  │       [3s timeout OR Hello received]  │
  │                                       │
```

### 2. Device Identification

```txt
  │                                       │
  │      [IdentifyingDevice State]        │
  │                                       │
  │                                     ┌─┴─┐
  │                                     │ DeviceIdentificationService
  │                                     │ - Extract IP: *************
  │                                     │ - Extract displayId (if Hello sent)
  │                                     │ - Lookup in devices.jsonc with AND logic
  │                                     │ - Match found
  │                                     └─┬─┘
  │                                       │
  │←─── Device Identification Message ───│
  │                                       │
```

### 3. Data Streaming Operation

```txt
  │                                       │
  │        [StreamingData State]          │
  │                                       │
  │←──────── OnboardData Update ─────────│
  │←──────── OnboardData Update ─────────│
  │←──────── OnboardData Update ─────────│
  │                ...                   │
  │                                       │
  │──── Bye Message (Optional) ─────────→│
```

### 4. Error Handling

```txt
  │                                       │
  │      [IdentifyingDevice State]        │
  │                                       │
  │                                     ┌─┴─┐
  │                                     │ DeviceIdentificationService
  │                                     │ - Extract IP: 192.168.1.999
  │                                     │ - Extract displayId (if Hello sent)
  │                                     │ - No match in devices.jsonc (AND logic)
  │                                     └─┬─┘
  │                                       │
  │←────────── Error Message ────────────│
  │←────── Connection Terminated ────────│
```

## Configuration

Device identification is configured via `devices.jsonc`:

```jsonc
[
    {
        "lookup": {
            "ip": "*************",
            "dns": "door-display-left-1", 
            "displayId": "display_001"
        },
        "deviceId": "door-left-display",
        "deviceClassifier": "onboard-display-information"
    }
]
```

**Lookup Rules:**

- All specified lookup fields must match for device identification (AND operation)
- IP address is the primary identification mechanism
- DNS and displayId are optional additional constraints
- First matching entry is used (order matters)
- Only non-None fields in DeviceLookup are considered for matching

## Error Handling

### Identification Errors

- **DEVICE_NOT_FOUND**: No device configuration matches connection parameters
- **IDENTIFICATION_TIMEOUT**: Device identification process exceeded timeout
- **CONFIG_ERROR**: Invalid device configuration file

### Connection Errors  

- **HANDSHAKE_FAILED**: WebSocket handshake unsuccessful
- **CONNECTION_LOST**: Connection terminated unexpectedly
- **SEND_FAILED**: Failed to send data to client

### Recovery Strategies

- Automatic retry for transient identification failures
- Graceful connection termination for permanent errors
- Logging and monitoring for operational visibility

## Implementation Notes

### Thread Safety

- All shared state protected by appropriate synchronization primitives
- Lock-free communication via channels where possible
- Careful mutex ordering to prevent deadlocks

### Performance Considerations

- Async/await for non-blocking I/O operations
- Efficient JSON serialization/deserialization
- Minimal data copying in broadcast operations
- Connection pooling and reuse where applicable

### Efficient Receive Handling Strategies

The protocol supports optional client messages (Hello and Bye) that enhance device identification and provide graceful disconnect reasons. While client messages are not required for basic operation, they are part of the protocol and must be handled when present. The receive handling should efficiently process these optional messages while maintaining WebSocket compliance.

#### 1. Integrated Select Loop (Recommended)

```rust
async fn handle_connection_with_protocol(
    ws_stream: WebSocketStream<TcpStream>,
    protocol_tx: UnboundedSender<ProtocolMessage>,
    data_rx: UnboundedReceiver<OnboardData>,
    connection_id: ConnectionId,
) -> Result<()> {
    let (mut outgoing, mut incoming) = ws_stream.split();
    let (tx, mut rx) = mpsc::unbounded_channel::<Message>();
    
    // Single task handles both send and receive with protocol awareness
    loop {
        tokio::select! {
            // Send path - outgoing data messages
            msg = rx.recv() => {
                match msg {
                    Some(message) => {
                        if outgoing.send(message).await.is_err() {
                            break; // Connection closed
                        }
                    }
                    None => break, // Channel closed
                }
            }
            
            // Receive path - handle protocol messages and WebSocket control frames
            result = incoming.next() => {
                match result {
                    Some(Ok(msg)) => {
                        match msg {
                            Message::Text(text) => {
                                // Parse and handle protocol messages (Hello, Bye)
                                if let Ok(protocol_msg) = parse_client_message(&text) {
                                    match protocol_msg {
                                        ClientMessage::Hello { display_id, .. } => {
                                            info!("Received Hello from displayId: {}", display_id);
                                            // Forward to protocol handler for device identification
                                            let _ = protocol_tx.send(ProtocolMessage::HelloReceived {
                                                connection_id,
                                                hello_data: protocol_msg,
                                            });
                                        }
                                        ClientMessage::Bye { reason } => {
                                            info!("Received Bye: {}", reason.unwrap_or_default());
                                            // Log graceful disconnect reason
                                            let _ = protocol_tx.send(ProtocolMessage::ByeReceived {
                                                connection_id,
                                                reason,
                                            });
                                            break; // Client initiated disconnect
                                        }
                                    }
                                } else {
                                    warn!("Received invalid protocol message: {}", text);
                                }
                            }
                            Message::Close(close_frame) => {
                                info!("Client sent close frame: {:?}", close_frame);
                                break;
                            }
                            Message::Ping(data) => {
                                // WebSocket spec requires responding to pings
                                if outgoing.send(Message::Pong(data)).await.is_err() {
                                    break;
                                }
                            }
                            Message::Pong(_) => {
                                // Acknowledge pong for connection health monitoring
                                trace!("Received pong from client");
                            }
                            Message::Binary(_) => {
                                // Protocol uses JSON text messages only
                                warn!("Received unexpected binary message from client");
                            }
                        }
                    }
                    Some(Err(e)) => {
                        warn!("WebSocket receive error: {}", e);
                        break;
                    }
                    None => {
                        info!("WebSocket stream ended");
                        break;
                    }
                }
            }
        }
    }
    
    // Notify protocol handler of connection termination
    let _ = protocol_tx.send(ProtocolMessage::ConnectionClosed { connection_id });
    Ok(())
}

fn parse_client_message(text: &str) -> Result<ClientMessage, serde_json::Error> {
    serde_json::from_str(text)
}

#[derive(Deserialize)]
#[serde(tag = "type")]
enum ClientMessage {
    #[serde(rename = "hello")]
    Hello {
        #[serde(rename = "displayId")]
        display_id: String,
        // Additional metadata fields can be captured as HashMap
        #[serde(flatten)]
        metadata: HashMap<String, serde_json::Value>,
    },
    #[serde(rename = "bye")]
    Bye {
        reason: Option<String>,
    },
}
```

#### 2. Protocol State-Aware Handling

The receive handling should be aware of the current protocol state to optimize message processing:

```rust
// In AwaitDeviceHello state - prioritize Hello messages
match protocol_state {
    ProtocolState::AwaitDeviceHello { .. } => {
        // Fast path for Hello messages during identification phase
        if let Message::Text(text) = &msg {
            if text.contains("\"type\":\"hello\"") {
                // Priority processing for Hello during identification
                return handle_hello_message(text, connection_id).await;
            }
        }
    }
    ProtocolState::StreamingData { .. } => {
        // In streaming state, Hello/Bye are less critical
        // Can be processed normally without priority handling
    }
    _ => {}
}
```

#### 3. WebSocket Configuration for Protocol Requirements

Configure the WebSocket stream to handle protocol-specific requirements:

```rust
use tokio_tungstenite::tungstenite::protocol::WebSocketConfig;

let config = WebSocketConfig {
    max_send_queue: Some(16),
    max_message_size: Some(64 << 10), // 64KB limit for onboard data
    max_frame_size: Some(16 << 10),   // 16KB frames
    accept_unmasked_frames: false,    // Security requirement
};

let ws_stream = accept_async_with_config(raw_stream, Some(config)).await?;
```

**Key Benefits:**

- **Protocol Compliance**: Properly handles Hello and Bye messages as defined in the protocol
- **Efficient Resource Usage**: Single task per connection handles both send and receive
- **WebSocket Standard Compliance**: Handles all required control frames (ping/pong/close)
- **State-Aware Processing**: Optimizes message handling based on current protocol state
- **Graceful Disconnect Handling**: Captures and logs client-provided disconnect reasons
- **Enhanced Device Identification**: Processes Hello messages for improved device matching
- **Connection Health Monitoring**: Detects connection issues through both control frames and message failures

### Monitoring and Observability

- Structured logging with tracing spans
- Connection lifecycle events
- Protocol state transitions
- Performance metrics (connection count, message rates)
- Error rates and types

## Future Extensions

### Enhanced Device Features

- Device capability negotiation
- Display-specific data filtering
- Client-initiated configuration updates

### Protocol Improvements

- Message compression for large payloads
- Delta updates for efficient bandwidth usage

### Security Enhancements

- TLS/WSS encryption
- Device authentication beyond IP-based identification
- Rate limiting and DoS protection
