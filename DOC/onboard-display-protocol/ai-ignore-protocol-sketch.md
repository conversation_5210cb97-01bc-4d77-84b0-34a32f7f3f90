# The communication protocol of WebDisplay with Onboard data server

## High level overview of the communication

This section - overiew - shows communication from the high level, without much words about internal server application architecture.

1. /device/ Connects to the websocket of onboard data server
2. /server/ Accepts connection and waits for the message, connection is given an ID
3. /device/ Might sends the Hello call with metadata, if it sends the Hello call, the displayId is mandatory (pay attention to the difference between displayId and deviceId); The protocol shall wait for Hello no longer than 3s. Then continue. The late Hello message is then only reported as a warning.
4. /server/ Finds peer IP, stores it in the connection metadata (hostname identification is optional and not implemented initially),
5. /server/ Matches available metadata against configuration rules and sends back to the device the OnboardDeviceIdentification message
6. /device/ receives metadata; metadata change like change in device classifier might trigger display reconfiguration. Display while reconfiguring will reload and from the /server/ side it means disconnection of client and later connection of the client. but server doesn't care. the new connection later is treated no different than any other. Sames sequence of actions just next time the display device will receive device identification data that it already has so it won't change the layout and continue to work.
8. /server/ Enters StreamingData state and sends available OnboardData to the display, then sends data when it is updated.

Before each disconnection /device/ may send "Bye" message with reason, but it doesn't have to. /server/ shall just save the message in the connection metadata.

## Details
This details section sketches some relevant details about the protocol.
It is not intended to be full description. It is meant to emphasize on matter relevant to the designer.

### Messages
Messages shall be simple in structure.
shall use camelCase

Client sends 'hello' message
{
    'type': 'hello', // enum
    'displayId': '.....'
    'someMetadata1': 'sdasdf'
}

Server 'device-identification': Rust type OnboardDeviceIdentification - where the server tells the device what it is supposed to be (device id, device classifier)
{
  'type': 'device-identification', // enum
  'deviceId': '***********', // string, the id may be anything
  'deviceClassifier': 'door-display' // string
}

Client 'bye' message
{
  'type': 'bye',
  'reason': 'changing configuration'
}

Server 'onboard-data' message - onboard data - where the actual trip or non trip, bt operational information is broadcasted continuously 
{
  'type': 'onboard-data',
  'onboard': {...}
}

## Data phase

## Implementation architecture
There is a need for separation of concerns for understandability of code.


### ConnectionHandling layer
Handles the new WebSocket connections, identifies the IP, adds ID. Gracefully closes every connection when requested.
Handles recv for each connection sending messages to the associated Connection interface

Hands over events to the Protocol Layer over Connection interface
Events: on_connect(Connection) - , on_disconnect, on_message

The instance of Protocol is created for the Connection.
### OnboardDisplayProtocol layer
OnboardDisplayProtocol is simple stateful protocol.
It serves two purposes:
- provides device identification data for the WebClients connecting to the protocol
- then continuously streams OnboardData when data changes (details of the model doesn't matter)

Device identification is 


States and what can happen:
/connection close may happen any time/
1 AwaitDeviceHello
  - timeout - the device doesn't have to send hello! after timeout. The identification can be continued without device hello message 
2 IdentifyingDevice
  - server can close the connection (If device is not identifiable - doesn't match config e.g. devices.jsonc); but we may also want a feature to accept every client so there also can be a default matching that everything else falls into
  - server sends the device identification
3 StreamingData
  - continuously stream display data updates (OnboardData) when data changes