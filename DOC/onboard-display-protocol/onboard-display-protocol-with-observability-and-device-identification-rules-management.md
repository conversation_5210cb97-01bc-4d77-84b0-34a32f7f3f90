# Onboard Display Protocol

## Overview

The Onboard Display Protocol provides a WebSocket-based communication channel between the Onboard Display Server and display devices. The protocol is designed to be simple, efficient, and reliable for real-time passenger information displays with comprehensive observability and dynamic configuration capabilities.

**Key Characteristics:**

- WebSocket-based communication
- Flexible device identification (IP-based with optional Hello message enhancement)
- Stateful protocol with clear lifecycle management
- Layered architecture for separation of concerns
- Real-time data updates to connected displays
- **Comprehensive observability** for connection monitoring and debugging
- **Dynamic configuration management** with live re-evaluation of connections

## Architecture Overview

The enhanced protocol implementation follows a layered architecture pattern with additional management and coordination layers:

```txt
┌─────────────────────────────────────────────────────────────┐
│                    Core Application                         │
│  ┌─────────────────────────────────────────────────────────┤
│  │           Management Interface Layer                    │
│  │  ┌─────────────────┬─────────────────────────────────────┤
│  │  │ObservabilityAPI │    ConfigurationAPI                │
│  │  └─────────────────┴─────────────────────────────────────┤
│  └─────────────────────────────────────────────────────────┤
│  │           Connection Registry Layer                     │
│  │  ┌─────────────────┬─────────────────┬─────────────────┤
│  │  │ConnectionRegistry│EventCoordinator │ReEvaluationEnginE│
│  │  └─────────────────┴─────────────────┴─────────────────┤
│  └─────────────────────────────────────────────────────────┤
│  │             Data Broadcasting Layer                     │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                    Protocol Layer                           │
│  ┌─────────────────────────────────────────────────────────┤
│  │           OnboardDisplayProtocol                        │
│  │  ┌─────────────────┬─────────────────┬─────────────────┤
│  │  │AwaitDeviceHello │IdentifyingDevice│ StreamingData   │
│  │  │    State        │     State       │     State       │
│  │  │                 │                 │ +ReIdentifying  │
│  │  └─────────────────┴─────────────────┴─────────────────┤
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│              Device Identification Layer                    │
│  ┌─────────────────────────────────────────────────────────┤
│  │           DeviceIdentificationService                   │
│  │  ┌─────────────────┬─────────────────────────────────────┤
│  │  │ConfigurationMgr │  BulkIdentificationService         │
│  │  └─────────────────┴─────────────────────────────────────┤
│  └─────────────────────────────────────────────────────────┤
│                                                             │
├─────────────────────────────────────────────────────────────┤
│                 Connection Layer                            │
│  ┌─────────────────────────────────────────────────────────┤
│  │             WebSocketConnection                         │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Enhanced Layer Details

### 1. Management Interface Layer (NEW)

**Responsibility:** Provides clean external APIs for observability and configuration management

```rust
trait ObservabilityService {
    async fn get_all_connections(&self) -> Vec<ConnectionInfo>;
    async fn get_connection(&self, id: ConnectionId) -> Option<ConnectionInfo>;
    async fn get_connections_by_device_classifier(&self, classifier: &str) -> Vec<ConnectionInfo>;
    async fn get_connection_statistics(&self) -> ConnectionStatistics;
}

trait ConfigurationService {
    async fn get_device_rules(&self) -> DeviceConfig;
    async fn set_device_rules(&self, rules: DeviceConfig) -> Result<(), ConfigError>;
    async fn validate_device_rules(&self, rules: &DeviceConfig) -> Result<(), ValidationError>;
}
```

**Connection Information Structure:**
```rust
struct ConnectionInfo {
    connection_id: ConnectionId,
    socket_addr: SocketAddr,
    connected_since: Instant,
    display_id: Option<String>,
    client_metadata: HashMap<String, serde_json::Value>,
    device_identification: Option<OnboardDeviceIdentification>,
    protocol_state: ProtocolState,
    last_message_sent: Option<Instant>,
    last_message_type: Option<String>,
    message_count: u64,
    bytes_sent: u64,
}

struct ConnectionStatistics {
    total_connections: usize,
    active_connections: usize,
    identified_connections: usize,
    connections_by_classifier: HashMap<String, usize>,
    total_messages_sent: u64,
    total_bytes_sent: u64,
    average_connection_duration: Duration,
}
```

### 2. Connection Registry Layer (NEW)

**Responsibility:** Central coordination for connection lifecycle, state tracking, and event management

```rust
struct ConnectionRegistry {
    connections: Arc<RwLock<HashMap<ConnectionId, ConnectionInfo>>>,
    event_coordinator: Arc<EventCoordinator>,
    statistics: Arc<RwLock<ConnectionStatistics>>,
}

struct EventCoordinator {
    event_bus: broadcast::Sender<SystemEvent>,
    subscribers: Arc<RwLock<HashMap<String, Vec<EventSubscriber>>>>,
}

struct ReEvaluationEngine {
    device_identification: Arc<dyn DeviceIdentificationService>,
    connection_registry: Arc<ConnectionRegistry>,
    event_coordinator: Arc<EventCoordinator>,
}

enum SystemEvent {
    ConnectionEstablished { connection_id: ConnectionId, info: ConnectionInfo },
    DeviceIdentified { connection_id: ConnectionId, device: OnboardDeviceIdentification },
    ConfigurationChanged,
    ConnectionTerminated { connection_id: ConnectionId, reason: String },
}
```

**Simplified Re-evaluation Process:**
1. Configuration change triggers `ConfigurationChanged` event
2. `ReEvaluationEngine` receives event and queries all active connections
3. Bulk re-identification performed via `BulkIdentificationService`
4. All protocol instances transition back to `IdentifyingDevice` state
5. Each connection re-identifies and sends updated device-identification message
6. Connections return to `StreamingData` state with new device info

### 3. Enhanced Data Broadcasting Layer

**Responsibility:** Manages data distribution with dynamic device classification awareness

```rust
struct DataBroadcastManager {
    active_protocols: HashMap<ConnectionId, UnboundedSender<OnboardData>>,
    connections_by_classifier: HashMap<String, HashSet<ConnectionId>>,
    last_data: Arc<Mutex<Option<OnboardData>>>,
    event_coordinator: Arc<EventCoordinator>,
}
```

**Enhanced Operations:**
- Receive `OnboardData` updates from core application
- Maintain classifier-based connection groups for targeted broadcasting
- Handle device classification changes via system events
- Update routing tables when device classifications change
- Support classifier-specific data filtering (future extension)

### 4. Enhanced Protocol Layer

**Responsibility:** Implements the stateful communication protocol with re-identification support

```rust
enum ProtocolState {
    AwaitDeviceHello {
        start_time: Instant,
        timeout_duration: Duration, // 3 seconds
    },
    IdentifyingDevice {
        start_time: Instant,
        identification_service: Arc<DeviceIdentificationService>,
        hello_data: Option<HelloMessage>,
    },
    StreamingData {
        device_info: OnboardDeviceIdentification,
        last_data_sent: Option<Instant>,
    },
}
```

**Simplified State Machine:**

```txt
  ┌─────────────────┐
  │AwaitDeviceHello │
  └─────────┬───────┘
            │ connection established
            │ wait 3s for optional Hello
            │ (Hello received OR timeout)
            ▼
  ┌─────────────────┐
  │IdentifyingDevice│◄──┐ 
  └─────────┬───────┘   │ 
            │ device identified
            ▼           │ 
  ┌─────────────────┐   │ 
  │ StreamingData   │   │ 
  └─────────┬───────┘   │ 
            │           │
            │ re-identification required (config change)
            └───────────┘
```

**Re-identification Handling:**
- When configuration changes, protocol transitions back to `IdentifyingDevice` state
- Re-identification always succeeds with new device information
- Sends updated device-identification message to client
- Returns to `StreamingData` state with new device info

### 5. Enhanced Device Identification Layer

**Responsibility:** Identifies devices with dynamic configuration management and bulk operations

```rust
struct DeviceIdentificationService {
    configuration_manager: Arc<ConfigurationManager>,
    bulk_identification_service: Arc<BulkIdentificationService>,
}

struct ConfigurationManager {
    current_config: Arc<RwLock<DeviceConfig>>,
    change_notifier: broadcast::Sender<()>,
    config_file_path: PathBuf,
}

struct BulkIdentificationService {
    identification_service: Arc<DeviceIdentificationService>,
}
```

**Simplified Operations:**
- Atomic replacement of entire device rule set
- Bulk re-identification of all connections when rules change
- Simple change event propagation (no versioning or detailed change tracking)
- Configuration persistence and reload capabilities

### 6. Connection Layer (Enhanced)

**Responsibility:** Manages low-level WebSocket connections with enhanced observability integration

```rust
struct WebSocketConnection {
    socket_addr: SocketAddr,
    ws_stream: WebSocketStream<TcpStream>,
    send_channel: UnboundedSender<Message>,
    connection_registry: Arc<ConnectionRegistry>,
    connection_id: ConnectionId,
}
```

**Enhanced Operations:**
- Connection lifecycle reporting to registry
- Message statistics tracking
- Connection health monitoring with detailed metrics
- Graceful shutdown coordination with upper layers

## Enhanced Message Types

### 1. Server to Client Messages

#### Device Identification (Unchanged)

```json
{
    "type": "device-identification", 
    "deviceId": "*************",
    "deviceClassifier": "onboard-display-information"
}
```

*Note: No additional message types needed. The existing device-identification message is sent whenever device identification occurs, whether initial or re-identification due to configuration changes.*

### 2. Client to Server Messages (Unchanged)

The existing Hello and Bye messages remain unchanged, maintaining backward compatibility.

## Enhanced Connection Lifecycle

### 1. Connection Establishment with Registry

```txt
Client                                  Server
  │                                       │
  │─────── WebSocket Handshake ─────────→│
  │←─────── Handshake Response ──────────│
  │                                       │
  │                                     ┌─┴─┐
  │                                     │ ConnectionRegistry
  │                                     │ - Register new connection
  │                                     │ - Assign ConnectionId
  │                                     │ - Initialize ConnectionInfo
  │                                     │ - Emit ConnectionEstablished event
  │                                     └─┬─┘
  │                                       │
  │        [AwaitDeviceHello State]       │
```

### 2. Simplified Device Re-identification Process

```txt
  │                                       │
  │        [StreamingData State]          │
  │                                       │
  │                                     ┌─┴─┐
  │                                     │ ConfigurationService
  │                                     │ - Configuration updated
  │                                     │ - Emit ConfigurationChanged event
  │                                     └─┬─┘
  │                                       │
  │                                     ┌─┴─┐
  │                                     │ ReEvaluationEngine
  │                                     │ - Trigger re-identification for all connections
  │                                     └─┬─┘
  │                                       │
  │     [IdentifyingDevice State]         │
  │←── Device Identification Message ────│
  │                                       │
  │     [StreamingData State]             │
  │     (with updated device info)        │
```

### 3. Observability Access

```txt
Management Client                        Server
  │                                       │
  │─────── Get All Connections ─────────→│
  │                                     ┌─┴─┐
  │                                     │ ObservabilityService
  │                                     │ - Query ConnectionRegistry
  │                                     │ - Aggregate connection information
  │                                     │ - Include statistics and metadata
  │                                     └─┬─┘
  │                                       │
  │←─────── Connection Information ──────│
  │  [                                    │
  │    {                                  │
  │      "connectionId": "conn-123",      │
  │      "socketAddr": "*************",  │
  │      "connectedSince": "2024-01-15T10:00:00Z",
  │      "displayId": "display_001",      │
  │      "deviceId": "door-left-display", │
  │      "deviceClassifier": "onboard-display-information",
  │      "protocolState": "StreamingData",│
  │      "lastMessageSent": "2024-01-15T10:30:00Z",
  │      "messageCount": 1250,            │
  │      "bytesSent": 2048576             │
  │    }                                  │
  │  ]                                    │
```

## Configuration Management

### Simplified Device Configuration

The device configuration system supports atomic replacement of the entire rule set:

```jsonc
[
  {
    "lookup": {
      "ip": "*************",
      "dns": "door-display-left-1", 
      "displayId": "display_001"
    },
    "deviceId": "door-left-display",
    "deviceClassifier": "onboard-display-information"
  }
]
```

### Simplified Configuration API

```rust
impl ConfigurationService {
    // Core Operations
    async fn get_device_rules(&self) -> DeviceConfig;
    async fn set_device_rules(&self, rules: DeviceConfig) -> Result<(), ConfigError>;
    
    // Validation
    async fn validate_device_rules(&self, rules: &DeviceConfig) -> Result<(), ValidationError>;
}
```

*Note: Configuration changes trigger immediate re-evaluation of all connections. No impact analysis needed - the system simply re-identifies all connections with the new rules.*

## Enhanced Error Handling

### Configuration Errors

- **INVALID_CONFIGURATION**: Configuration validation failed
- **DEVICE_RULE_CONFLICT**: Multiple rules match same device criteria

### Observability Errors

- **CONNECTION_NOT_FOUND**: Requested connection does not exist
- **STATISTICS_UNAVAILABLE**: Connection statistics temporarily unavailable
- **QUERY_TIMEOUT**: Observability query exceeded timeout

*Note: Re-identification always succeeds, so no specific re-identification error codes are needed.*

## Enhanced Implementation Notes

### Event System Design

The event system uses a publish-subscribe pattern with typed events:

```rust
trait EventSubscriber {
    async fn handle_event(&self, event: SystemEvent) -> Result<(), EventError>;
}

struct EventCoordinator {
    subscribers: Arc<RwLock<HashMap<String, Vec<Box<dyn EventSubscriber>>>>>,
    event_queue: Arc<Mutex<VecDeque<SystemEvent>>>,
    processing_task: JoinHandle<()>,
}
```

### Configuration Persistence

- Atomic configuration updates with backup creation
- Configuration versioning for change tracking
- Rollback capabilities for failed updates
- File watching for external configuration changes

### Performance Considerations

- **Efficient Bulk Operations**: Re-identification batched to minimize disruption
- **Lock-free Statistics**: Connection statistics using atomic operations where possible
- **Event Batching**: Multiple configuration changes batched into single re-evaluation
- **Lazy Loading**: Connection metadata loaded on-demand for observability queries
- **Memory-efficient Storage**: Connection information stored with minimal overhead

### Security Considerations

- **Configuration Access Control**: Separate read/write permissions for configuration API
- **Audit Logging**: All configuration changes logged with timestamps and user information
- **Validation Boundaries**: Strict validation of all external configuration inputs
- **Rate Limiting**: Management API protected against excessive requests

## Future Extensions

### Advanced Observability

- Real-time connection monitoring dashboard
- Historical connection analytics and reporting
- Performance metrics and alerting
- Connection tracing and debugging tools

### Enhanced Configuration Management

- Configuration templates and inheritance
- Conditional device rules based on time/state
- Configuration deployment and rollback workflows
- Multi-environment configuration management

### Protocol Improvements

- Message compression for large payloads
- Delta updates for efficient bandwidth usage
- Device capability negotiation
- Client-initiated configuration updates

### Security Enhancements

- TLS/WSS encryption
- Device authentication beyond IP-based identification
- Rate limiting and DoS protection
- Certificate-based device identification
