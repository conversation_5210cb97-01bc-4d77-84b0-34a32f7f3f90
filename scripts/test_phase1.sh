#!/bin/bash

# Phase 1 Protocol Test Script
echo "=== Phase 1 Onboard Display Protocol Test ==="

SERVER_URL="ws://127.0.0.1:9001"

echo "Starting test server in background..."
# Start the server with HTTP data source for testing
cargo run -- --data-source http --vehicle-id "test-vehicle" --addr "127.0.0.1:9001" &
SERVER_PID=$!

# Wait for server to start
sleep 3

echo "Test 1: Basic WebSocket connection"
echo "This should connect and receive onboard data messages..."
timeout 5 websocat "$SERVER_URL" | head -3

echo ""
echo "Test 2: Multiple concurrent connections"
echo "Testing multiple connections simultaneously..."
for i in {1..3}; do
    timeout 2 websocat "$SERVER_URL" | head -1 &
done
wait

echo ""
echo "Shutting down test server..."
kill $SERVER_PID 2>/dev/null

echo "=== Phase 1 tests completed ==="
echo ""
echo "Expected behavior:"
echo "- Connections should succeed"
echo "- Should receive onboard data with proper JSON format"
echo "- Multiple connections should work simultaneously"
echo "- Current implementation does not yet include device identification"
echo "- Next phase will implement protocol state machine and device identification" 